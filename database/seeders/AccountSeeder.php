<?php

namespace Database\Seeders;

use App\Jobs\PopulateAccountWithDefaultProductVariants;
use App\Models\Account;
use App\Models\Dealership;
use App\Models\User;
use Illuminate\Database\Seeder;

class AccountSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        foreach (['Demo Company'] as $index => $accountName) {
            $account = Account::factory()->create([
                'name' => $accountName,
                'warranty_self_funded' => true,
            ]);

            if ($index === 0) {
                User::factory()->create([
                    'account_id' => null,
                    'first_name' => '<PERSON>',
                    'last_name' => '<PERSON>',
                    'email' => '<EMAIL>',
                ])->assignRole('Admin');
            }

            $users = User::factory(5)
                ->create(['account_id' => $account->id])
                ->each(function ($user, $index) {
                    if ($index === 0) {
                        $user->assignRole('Dealership Manager');
                    } else {
                        $user->assignRole('Dealership Sales');
                    }
                });

            $dealerships = Dealership::factory(2)
                ->create(['account_id' => $account->id]);

            PopulateAccountWithDefaultProductVariants::dispatchSync($account);
        }
    }
}
