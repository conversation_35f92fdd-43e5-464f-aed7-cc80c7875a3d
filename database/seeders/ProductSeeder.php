<?php

namespace Database\Seeders;

use App\Models\ProductGroup;
use Illuminate\Database\Seeder;

class ProductSeeder extends Seeder
{
    protected $productGroups = [
        [
            'name' => 'Paint Protection',
            'products' => [
                [
                    'name' => 'Paint Protection',
                    'cost_price' => 40.00,
                    'selling_price' => 199.00,
                    'dealer_commission' => null,
                ],
            ],
        ],
        [
            'name' => 'EV Charger',
            'products' => [
                [
                    'name' => 'Ohme EV Charger (Tethered)',
                    'cost_price' => 599.00,
                    'selling_price' => 799.00,
                    'dealer_commission' => null,
                ],
                [
                    'name' => 'Ohme EV Charger (Untethered)',
                    'cost_price' => 499.00,
                    'selling_price' => 699.00,
                    'dealer_commission' => null,
                ],
            ],
        ],
    ];

    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        foreach ($this->productGroups as $productGroup) {
            $productGroupModel = ProductGroup::firstOrCreate([
                'name' => $productGroup['name'],
            ]);
            foreach ($productGroup['products'] as $product) {
                $productGroupModel->products()->updateOrCreate([
                    'name' => $product['name'],
                ], [
                    'cost_price' => $product['cost_price'],
                    'selling_price' => $product['selling_price'],
                    'dealer_commission' => $product['dealer_commission'],
                ]);
            }
        }

        $names = collect($this->productGroups)
            ->pluck('products.*.name')
            ->flatten();

        \App\Models\Product::whereNotIn('name', $names)->forceDelete();
    }
}
