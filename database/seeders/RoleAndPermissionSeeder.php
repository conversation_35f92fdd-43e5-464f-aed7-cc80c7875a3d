<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleAndPermissionSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->setupPermissionsAndRoles();
    }

    private function setupPermissionsAndRoles()
    {
        $permissions = [
            'Admin' => [
                'dashboard.view',
                'accounts.manage',
                'dealerships.view',
                'dealerships.create',
                'dealerships.update',
                'dealerships.delete',
                'dealerships.restore',
                'sales-people.manage',
                'repairers.view',
                'repairers.manage',
                'products.manage',
                'customers.create',
                'customers.view',
                'customers.update',
                'sales.manage',
                'sales.view',
                'sales.create',
                'sales.update',
                'claims.view',
                'claims.update',
                'claims.create',
                'claims.delete',
                'claims.uses-browser-plugin',
                'claim-estimates.view',
                'claim-estimates.create',
                'claim-estimates.update',
                'claim-estimates.delete',
                'claim-authorisations.view',
                'claim-authorisations.create',
                'claim-authorisations.update',
                'claim-rejections.create',
                'breakdown-claims.view',
                'breakdown-claims.create',
                'breakdown-claims.update',
                'service-plan-redemptions.view',
                'service-plan-redemptions.create',
                'service-plan-redemptions.update',
                'invoices.view',
                'invoices.create',
                'payments.view',
                'users.manage',
                'warranties.export',
                'breakdownPlans.export',
                'claims.export',
                'component-list.view',
            ],
            'Owner' => [
                'admin-users.manage',
                'users.impersonate',
                'accounts.update-funding-types',
                'payments.update',
            ],
            'Developer' => [
                'admin-users.manage',
                'users.impersonate',
                'accounts.update-funding-types',
            ],
            'Dealership Manager' => [
                'dashboard.view',
                'dealerships.view',
                'dealerships.create',
                'dealerships.update',
                'dealerships.delete',
                'dealerships.restore',
                'sales-people.manage',
                'repairers.view',
                'products.manage',
                'customers.create',
                'customers.view',
                'customers.update',
                'sales.manage',
                'sales.view',
                'sales.create',
                'claims.view',
                'claims.create',
                'claim-estimates.view',
                'claim-estimates.create',
                'claim-authorisations.view',
                'claim-authorisations.create',
                'claim-rejections.create',
                'breakdown-claims.view',
                'service-plan-redemptions.view',
                'invoices.view',
                'warranties.export',
                'breakdownPlans.export',
            ],
            'Dealership Sales' => [
                'customers.create',
                'customers.view',
                'customers.update',
                'sales.manage',
                'sales.view',
                'sales.create',
                'repairers.view',
                'claims.view',
                'claim-estimates.view',
            ],
        ];

        $internalRoles = [
            'Admin',
            'Owner',
            'Developer',
        ];

        $allPermissions = collect($permissions)->flatten()->unique()->values();

        foreach ($allPermissions as $permissionName) {
            Permission::firstOrCreate(['name' => $permissionName]);
        }
        Permission::whereNotIn('name', $allPermissions)->delete();

        foreach ($permissions as $roleName => $permissionNames) {
            Role::updateOrCreate([
                'name' => $roleName,
            ], [
                'is_internal' => in_array($roleName, $internalRoles),
            ])->syncPermissions($permissionNames);
        }

        if (! app()->isProduction()) {
            $this->syncUserRoles();
        }
    }

    private function syncUserRoles(): void
    {
        User::query()
            ->whereIn('email', [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
            ])->each(function (User $user) {
                $user->syncRoles('Admin', 'Owner', 'Developer');
                $user->update(['is_internal' => true]);
            });
    }
}
