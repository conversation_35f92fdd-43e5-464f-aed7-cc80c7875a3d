<?php

namespace Database\Seeders;

use App\Enums\VehicleType;
use App\Models\CoverLevel;
use App\Models\WarrantyProduct;
use App\Models\WarrantyProductVariant;
use Illuminate\Database\Seeder;

class WarrantyProductSeeder extends Seeder
{
    protected $products = [
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Platinum',
            'period' => 36,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 1799,
                    'admin_fee' => 988.27,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 999,
                    'admin_fee' => 450,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 999,
                    'admin_fee' => 495,
                ],
            ],
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Platinum',
            'period' => 24,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 1299,
                    'admin_fee' => 603.94,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 699,
                    'admin_fee' => 275,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 999,
                    'admin_fee' => 302.50,
                ],
            ],
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Platinum',
            'period' => 15,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 899,
                    'admin_fee' => 384.33,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 399,
                    'admin_fee' => 175,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 399,
                    'admin_fee' => 192.50,
                ],
            ],
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Platinum',
            'period' => 12,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 899,
                    'admin_fee' => 384.33,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 399,
                    'admin_fee' => 175,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 399,
                    'admin_fee' => 192.50,
                ],
            ],
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Platinum',
            'period' => 6,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 0,
                    'admin_fee' => 217.52,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 0,
                    'admin_fee' => 120,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 999,
                    'admin_fee' => 132,
                ],
            ],
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Platinum',
            'period' => 3,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 0,
                    'admin_fee' => 164.71,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 0,
                    'admin_fee' => 95,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 0,
                    'admin_fee' => 104.50,
                ],
            ],
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Platinum',
            'period' => 1,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 0,
                    'admin_fee' => 164.71,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 0,
                    'admin_fee' => 95,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 0,
                    'admin_fee' => 104.50,
                ],
            ],
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Platinum',
            'period' => 1,
            'is_recurring' => true,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 0,
                    'admin_fee' => 0,
                    'monthly_selling_price' => 35,
                    'monthly_admin_fee' => 2,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 0,
                    'admin_fee' => 0,
                    'monthly_selling_price' => 25,
                    'monthly_admin_fee' => 2,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 0,
                    'admin_fee' => 0,
                    'monthly_selling_price' => 35,
                    'monthly_admin_fee' => 2,
                ],
            ],
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Gold',
            'period' => 36,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 1599,
                    'admin_fee' => 889.44,
                ],
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 1399,
                    'admin_fee' => 1087.09,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 849,
                    'admin_fee' => 405,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 849,
                    'admin_fee' => 445.50,
                ],
            ],
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Gold',
            'period' => 24,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 999,
                    'admin_fee' => 543.55,
                ],
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 899,
                    'admin_fee' => 664.34,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 699,
                    'admin_fee' => 247.50,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 649,
                    'admin_fee' => 272.25,
                ],
            ],
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Gold',
            'period' => 15,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 699,
                    'admin_fee' => 345.89,
                ],
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 699,
                    'admin_fee' => 422.76,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 349,
                    'admin_fee' => 157.50,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 349,
                    'admin_fee' => 173.25,
                ],
            ],
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Gold',
            'period' => 12,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 699,
                    'admin_fee' => 345.89,
                ],
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 699,
                    'admin_fee' => 422.76,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 349,
                    'admin_fee' => 157.50,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 2500,
                    'total_claim_limit' => 10000,
                    'selling_price' => 349,
                    'admin_fee' => 173.25,
                ],
            ],
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Gold',
            'period' => 6,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 0,
                    'admin_fee' => 217.42,
                ],
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 0,
                    'admin_fee' => 239.16,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 0,
                    'admin_fee' => 108,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 0,
                    'admin_fee' => 118.80,
                ],
            ],
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Gold',
            'period' => 3,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 0,
                    'admin_fee' => 148.24,
                ],
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 0,
                    'admin_fee' => 181.18,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 0,
                    'admin_fee' => 70,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 0,
                    'admin_fee' => 78,
                ],
            ],
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Gold',
            'period' => 1,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 0,
                    'admin_fee' => 0,
                    'monthly_selling_price' => 35,
                    'monthly_admin_fee' => 2,
                ],
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 0,
                    'admin_fee' => 0,
                    'monthly_selling_price' => 35,
                    'monthly_admin_fee' => 2,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 0,
                    'admin_fee' => 0,
                    'monthly_selling_price' => 35,
                    'monthly_admin_fee' => 2,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 0,
                    'admin_fee' => 0,
                    'monthly_selling_price' => 35,
                    'monthly_admin_fee' => 2,
                ],
            ],
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Gold',
            'period' => 1,
            'is_recurring' => true,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 25,
                    'admin_fee' => 2,
                ],
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 25,
                    'admin_fee' => 2,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 25,
                    'admin_fee' => 2,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 1000,
                    'total_claim_limit' => 10000,
                    'selling_price' => 25,
                    'admin_fee' => 2,
                ],
            ],
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Silver',
            'period' => 36,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 1299,
                    'admin_fee' => 822.56,
                ],
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 1500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 1299,
                    'admin_fee' => 978.38,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 799,
                    'admin_fee' => 375,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 1500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 799,
                    'admin_fee' => 412,
                ],
            ],
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Silver',
            'period' => 24,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 799,
                    'admin_fee' => 549.04,
                ],
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 1500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 849,
                    'admin_fee' => 597.90,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 499,
                    'admin_fee' => 250,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 1500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 599,
                    'admin_fee' => 275,
                ],
            ],
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Silver',
            'period' => 15,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 699,
                    'admin_fee' => 329.42,
                ],
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 1500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 649,
                    'admin_fee' => 380.48,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 249,
                    'admin_fee' => 150,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 1500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 399,
                    'admin_fee' => 165,
                ],
            ],
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Silver',
            'period' => 12,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 699,
                    'admin_fee' => 329.42,
                ],
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 1500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 649,
                    'admin_fee' => 380.48,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 1500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 249,
                    'admin_fee' => 150,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 1500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 399,
                    'admin_fee' => 165,
                ],
            ],
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Silver',
            'period' => 6,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 0,
                    'admin_fee' => 187.67,
                ],
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 0,
                    'admin_fee' => 215.24,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 0,
                    'admin_fee' => 90,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 0,
                    'admin_fee' => 94,
                ],
            ],
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Silver',
            'period' => 3,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 0,
                    'admin_fee' => 155.73,
                ],
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 0,
                    'admin_fee' => 163.06,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 0,
                    'admin_fee' => 70,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 0,
                    'admin_fee' => 78,
                ],
            ],
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Silver',
            'period' => 1,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 0,
                    'admin_fee' => 155.73,
                ],
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 0,
                    'admin_fee' => 163.06,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 0,
                    'admin_fee' => 70,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 0,
                    'admin_fee' => 78,
                ],
            ],
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Silver',
            'period' => 1,
            'is_recurring' => true,
            'variants' => [
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 0,
                    'admin_fee' => 0,
                    'monthly_selling_price' => 35,
                    'monthly_admin_fee' => 2,
                ],
                [
                    'max_engine_capacity' => null,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 0,
                    'admin_fee' => 0,
                    'monthly_selling_price' => 35,
                    'monthly_admin_fee' => 2,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 70000,
                    'max_age' => 72,
                    'individual_claim_limit' => 500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 0,
                    'admin_fee' => 0,
                    'monthly_selling_price' => 35,
                    'monthly_admin_fee' => 2,
                ],
                [
                    'max_engine_capacity' => 3000,
                    'max_mileage' => 100000,
                    'max_age' => 120,
                    'individual_claim_limit' => 500,
                    'total_claim_limit' => 5000,
                    'selling_price' => 0,
                    'admin_fee' => 0,
                    'monthly_selling_price' => 35,
                    'monthly_admin_fee' => 2,
                ],
            ],
        ],
        // EVs
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'EV Platinum',
            'period' => 36,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'EV Platinum',
            'period' => 24,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'EV Platinum',
            'period' => 15,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'EV Platinum',
            'period' => 12,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'EV Platinum',
            'period' => 6,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'EV Platinum',
            'period' => 3,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'EV Platinum',
            'period' => 1,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'EV Platinum',
            'period' => 1,
            'is_recurring' => true,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'EV Gold',
            'period' => 36,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'EV Gold',
            'period' => 24,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'EV Gold',
            'period' => 15,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'EV Gold',
            'period' => 12,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'EV Gold',
            'period' => 6,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'EV Gold',
            'period' => 3,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'EV Gold',
            'period' => 1,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'EV Gold',
            'period' => 1,
            'is_recurring' => true,
        ],
        // Hybrids
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Hybrid Platinum',
            'period' => 36,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Hybrid Platinum',
            'period' => 24,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Hybrid Platinum',
            'period' => 15,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Hybrid Platinum',
            'period' => 12,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Hybrid Platinum',
            'period' => 6,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Hybrid Platinum',
            'period' => 3,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Hybrid Platinum',
            'period' => 1,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Hybrid Platinum',
            'period' => 1,
            'is_recurring' => true,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Hybrid Gold',
            'period' => 36,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Hybrid Gold',
            'period' => 24,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Hybrid Gold',
            'period' => 15,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Hybrid Gold',
            'period' => 12,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Hybrid Gold',
            'period' => 6,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Hybrid Gold',
            'period' => 3,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Hybrid Gold',
            'period' => 1,
        ],
        [
            'vehicle_type' => VehicleType::CAR->value,
            'name' => 'Hybrid Gold',
            'period' => 1,
            'is_recurring' => true,
        ],
        [
            'vehicle_type' => VehicleType::MOTORHOME->value,
            'name' => 'Campervan / Motorhome Gold',
            'period' => 36,
        ],
        [
            'vehicle_type' => VehicleType::MOTORHOME->value,
            'name' => 'Campervan / Motorhome Gold',
            'period' => 24,
        ],
        [
            'vehicle_type' => VehicleType::MOTORHOME->value,
            'name' => 'Campervan / Motorhome Gold',
            'period' => 12,
        ],
        [
            'vehicle_type' => VehicleType::MOTORHOME->value,
            'name' => 'Campervan / Motorhome Gold',
            'period' => 6,
        ],
        [
            'vehicle_type' => VehicleType::MOTORHOME->value,
            'name' => 'Campervan / Motorhome Gold',
            'period' => 3,
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Platinum Light Commercial',
            'period' => 36,
            'variants' => [],
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Platinum Light Commercial',
            'period' => 24,
            'variants' => [],
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Platinum Light Commercial',
            'period' => 15,
            'variants' => [],
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Platinum Light Commercial',
            'period' => 12,
            'variants' => [],
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Platinum Light Commercial',
            'period' => 6,
            'variants' => [],
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Platinum Light Commercial',
            'period' => 3,
            'variants' => [],
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Platinum Light Commercial',
            'period' => 1,
            'variants' => [],
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Platinum Light Commercial',
            'period' => 1,
            'is_recurring' => true,
            'variants' => [],
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Gold Light Commercial',
            'period' => 36,
            'variants' => [],
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Gold Light Commercial',
            'period' => 24,
            'variants' => [],
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Gold Light Commercial',
            'period' => 15,
            'variants' => [],
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Gold Light Commercial',
            'period' => 12,
            'variants' => [],
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Gold Light Commercial',
            'period' => 6,
            'variants' => [],
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Gold Light Commercial',
            'period' => 3,
            'variants' => [],
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Gold Light Commercial',
            'period' => 1,
            'variants' => [],
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Gold Light Commercial',
            'period' => 1,
            'is_recurring' => true,
            'variants' => [],
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Silver Light Commercial',
            'period' => 36,
            'variants' => [],
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Silver Light Commercial',
            'period' => 24,
            'variants' => [],
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Silver Light Commercial',
            'period' => 15,
            'variants' => [],
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Silver Light Commercial',
            'period' => 12,
            'variants' => [],
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Silver Light Commercial',
            'period' => 6,
            'variants' => [],
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Silver Light Commercial',
            'period' => 3,
            'variants' => [],
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Silver Light Commercial',
            'period' => 1,
            'variants' => [],
        ],
        [
            'vehicle_type' => VehicleType::VAN->value,
            'name' => 'Silver Light Commercial',
            'period' => 1,
            'is_recurring' => true,
            'variants' => [],
        ],
    ];

    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        WarrantyProductVariant::truncate();

        foreach ($this->products as $index => $product) {
            $coverLevelModel = CoverLevel::firstWhere([
                'vehicle_type' => $product['vehicle_type'],
                'name' => $product['name'],
            ]);
            if (! $coverLevelModel) {
                throw new \Exception("Cover Level not found for {$product['name']}");
            }
            $dbProduct = WarrantyProduct::updateOrCreate([
                'cover_level_id' => $coverLevelModel->id,
                'period' => $product['period'],
                'is_recurring' => $product['is_recurring'] ?? false,
            ], [
                'position' => $index + 1,
            ]);
            foreach ($product['variants'] ?? [] as $variant) {
                $dbProduct->variants()->firstOrCreate([
                    'provision' => $variant['admin_fee'],
                    'max_engine_capacity' => $variant['max_engine_capacity'],
                    'max_mileage' => $variant['max_mileage'],
                    'max_age' => $variant['max_age'],
                    'individual_claim_limit' => $variant['individual_claim_limit'],
                    'total_claim_limit' => $variant['total_claim_limit'],
                    'selling_price' => $variant['selling_price'],
                    'admin_fee' => $variant['admin_fee'],
                    'monthly_selling_price' => $variant['monthly_selling_price'] ?? null,
                    'monthly_admin_fee' => $variant['monthly_admin_fee'] ?? null,
                ]);
            }
        }
    }
}
