CREATE TABLE IF NOT EXISTS "migrations"(
  "id" integer primary key autoincrement not null,
  "migration" varchar not null,
  "batch" integer not null
);
CREATE TABLE IF NOT EXISTS "password_reset_tokens"(
  "email" varchar not null,
  "token" varchar not null,
  "created_at" datetime
);
CREATE INDEX "password_resets_email_index" on "password_reset_tokens"("email");
CREATE TABLE IF NOT EXISTS "failed_jobs"(
  "id" integer primary key autoincrement not null,
  "uuid" varchar not null,
  "connection" text not null,
  "queue" text not null,
  "payload" text not null,
  "exception" text not null,
  "failed_at" datetime not null default CURRENT_TIMESTAMP
);
CREATE UNIQUE INDEX "failed_jobs_uuid_unique" on "failed_jobs"("uuid");
CREATE TABLE IF NOT EXISTS "accounts"(
  "id" integer primary key autoincrement not null,
  "name" varchar not null,
  "authorisation_contact" varchar not null,
  "max_hourly_labour_rate" numeric not null,
  "warranty_self_funded" tinyint(1) not null,
  "logo_path" varchar,
  "created_at" datetime,
  "updated_at" datetime,
  "deleted_at" datetime,
  "breakdown_self_funded" tinyint(1) not null,
  "send_contract_emails" tinyint(1) not null default '1',
  "claim_handling_notes" text
);
CREATE TABLE IF NOT EXISTS "permissions"(
  "id" integer primary key autoincrement not null,
  "name" varchar not null,
  "guard_name" varchar not null,
  "created_at" datetime,
  "updated_at" datetime
);
CREATE UNIQUE INDEX "permissions_name_guard_name_unique" on "permissions"(
  "name",
  "guard_name"
);
CREATE TABLE IF NOT EXISTS "roles"(
  "id" integer primary key autoincrement not null,
  "name" varchar not null,
  "guard_name" varchar not null,
  "created_at" datetime,
  "updated_at" datetime
);
CREATE UNIQUE INDEX "roles_name_guard_name_unique" on "roles"(
  "name",
  "guard_name"
);
CREATE TABLE IF NOT EXISTS "model_has_permissions"(
  "permission_id" integer not null,
  "model_type" varchar not null,
  "model_id" integer not null,
  foreign key("permission_id") references "permissions"("id") on delete cascade,
  primary key("permission_id", "model_id", "model_type")
);
CREATE INDEX "model_has_permissions_model_id_model_type_index" on "model_has_permissions"(
  "model_id",
  "model_type"
);
CREATE TABLE IF NOT EXISTS "model_has_roles"(
  "role_id" integer not null,
  "model_type" varchar not null,
  "model_id" integer not null,
  foreign key("role_id") references "roles"("id") on delete cascade,
  primary key("role_id", "model_id", "model_type")
);
CREATE INDEX "model_has_roles_model_id_model_type_index" on "model_has_roles"(
  "model_id",
  "model_type"
);
CREATE TABLE IF NOT EXISTS "role_has_permissions"(
  "permission_id" integer not null,
  "role_id" integer not null,
  foreign key("permission_id") references "permissions"("id") on delete cascade,
  foreign key("role_id") references "roles"("id") on delete cascade,
  primary key("permission_id", "role_id")
);
CREATE TABLE IF NOT EXISTS "fault_types"(
  "id" integer primary key autoincrement not null,
  "name" varchar not null,
  "created_at" datetime,
  "updated_at" datetime
);
CREATE UNIQUE INDEX "fault_types_name_unique" on "fault_types"("name");
CREATE TABLE IF NOT EXISTS "user_invites"(
  "email" varchar not null,
  "token" varchar not null,
  "created_at" datetime
);
CREATE INDEX "user_invites_email_index" on "user_invites"("email");
CREATE TABLE IF NOT EXISTS "account_warranty_products"(
  "id" integer primary key autoincrement not null,
  "account_id" integer not null,
  "product_id" integer not null,
  "admin_fee" numeric not null,
  "created_at" datetime,
  "updated_at" datetime,
  "total_claim_limit" numeric not null,
  "individual_claim_limit" numeric not null,
  "provision" numeric not null,
  "selling_price" numeric not null,
  "max_age" integer,
  "max_mileage" integer,
  "max_engine_capacity" integer,
  "annual_mileage_limit" integer,
  "monthly_selling_price" numeric,
  "monthly_admin_fee" numeric,
  foreign key("account_id") references "accounts"("id"),
  foreign key("product_id") references "warranty_products"("id")
);
CREATE UNIQUE INDEX "unique_aggregate_unique" on "account_warranty_products"(
  "account_id",
  "product_id",
  "max_age",
  "max_mileage",
  "max_engine_capacity"
);
CREATE TABLE IF NOT EXISTS "files"(
  "id" integer primary key autoincrement not null,
  "account_id" integer not null,
  "related_type" varchar not null,
  "related_id" integer not null,
  "name" varchar not null,
  "path" varchar not null,
  "size" integer not null,
  "created_at" datetime,
  "updated_at" datetime,
  "deleted_at" datetime,
  foreign key("account_id") references "accounts"("id")
);
CREATE INDEX "files_related_type_related_id_index" on "files"(
  "related_type",
  "related_id"
);
CREATE TABLE IF NOT EXISTS "breakdown_products"(
  "id" integer primary key autoincrement not null,
  "position" integer not null,
  "name" varchar not null,
  "period" integer not null,
  "created_at" datetime,
  "updated_at" datetime
);
CREATE TABLE IF NOT EXISTS "account_breakdown_products"(
  "id" integer primary key autoincrement not null,
  "account_id" integer not null,
  "breakdown_product_id" integer not null,
  "max_age" integer,
  "max_mileage" integer,
  "max_engine_capacity" integer,
  "provision" numeric not null,
  "selling_price" numeric not null,
  "admin_fee" numeric not null,
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("account_id") references "accounts"("id"),
  foreign key("breakdown_product_id") references "breakdown_products"("id")
);
CREATE TABLE IF NOT EXISTS "breakdown_claims"(
  "id" integer primary key autoincrement not null,
  "breakdown_plan_id" integer not null,
  "entered_by_id" integer not null,
  "failure_date" date not null,
  "failure_mileage" integer not null,
  "claim_number" varchar not null,
  "reference" varchar not null,
  "cost" numeric not null,
  "created_at" datetime,
  "updated_at" datetime,
  "vat" numeric not null,
  foreign key("breakdown_plan_id") references "breakdown_plans"("id"),
  foreign key("entered_by_id") references "users"("id")
);
CREATE TABLE IF NOT EXISTS "claim_rejections"(
  "id" integer primary key autoincrement not null,
  "claim_id" integer not null,
  "user_id" integer not null,
  "reason" varchar not null,
  "notes" text not null,
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("claim_id") references "claims"("id"),
  foreign key("user_id") references "users"("id")
);
CREATE TABLE IF NOT EXISTS "users"(
  "id" integer primary key autoincrement not null,
  "account_id" integer,
  "dealership_id" integer,
  "first_name" varchar not null,
  "last_name" varchar not null,
  "email" varchar not null,
  "email_verified_at" datetime,
  "password" varchar,
  "owner" tinyint(1) not null default('0'),
  "photo_path" varchar,
  "remember_token" varchar,
  "created_at" datetime,
  "updated_at" datetime,
  "deleted_at" datetime,
  foreign key("account_id") references "accounts"("id")
);
CREATE INDEX "users_account_id_index" on "users"("account_id");
CREATE TABLE IF NOT EXISTS "accounting_contacts"(
  "id" integer primary key autoincrement not null,
  "accounting_software_id" varchar not null,
  "name" varchar not null,
  "email" varchar,
  "address_line_1" varchar,
  "address_line_2" varchar,
  "address_line_3" varchar,
  "address_line_4" varchar,
  "address_city" varchar,
  "address_region" varchar,
  "address_postcode" varchar,
  "created_at" datetime,
  "updated_at" datetime
);
CREATE UNIQUE INDEX "accounting_contacts_accounting_software_id_unique" on "accounting_contacts"(
  "accounting_software_id"
);
CREATE TABLE IF NOT EXISTS "invoices"(
  "id" integer primary key autoincrement not null,
  "account_id" integer not null,
  "date" date not null,
  "due_date" date not null,
  "period_start" date,
  "period_end" date,
  "status" varchar not null,
  "description" varchar not null,
  "invoice_number" varchar,
  "accounting_software_id" varchar,
  "emailed_at" datetime,
  "created_at" datetime,
  "updated_at" datetime,
  "invoiceable_type" varchar not null,
  "invoiceable_id" integer not null,
  "gocardless_payment_id" varchar,
  "reference" varchar,
  "accounting_software_payment_id" varchar,
  foreign key("account_id") references accounts("id") on delete cascade on update no action
);
CREATE INDEX "invoices_invoiceable_type_invoiceable_id_index" on "invoices"(
  "invoiceable_type",
  "invoiceable_id"
);
CREATE TABLE IF NOT EXISTS "customers"(
  "id" integer primary key autoincrement not null,
  "account_id" integer not null,
  "email" varchar not null,
  "phone" varchar not null,
  "first_name" varchar not null,
  "last_name" varchar not null,
  "address_1" varchar not null,
  "address_2" varchar,
  "city" varchar not null,
  "county" varchar not null,
  "country" varchar not null,
  "postcode" varchar not null,
  "created_at" datetime,
  "updated_at" datetime,
  "accounting_contact_id" integer,
  foreign key("account_id") references accounts("id") on delete no action on update no action,
  foreign key("accounting_contact_id") references "accounting_contacts"("id") on delete set null
);
CREATE TABLE IF NOT EXISTS "payouts"(
  "id" integer primary key autoincrement not null,
  "processor_payout_id" varchar not null,
  "reference" varchar not null,
  "status" varchar not null,
  "arrival_date" date not null,
  "amount" numeric not null,
  "deducted_fees" numeric not null,
  "created_at" datetime,
  "updated_at" datetime,
  "accounting_software_transfer_id" varchar,
  "accounting_software_from_transaction_id" varchar,
  "accounting_software_to_transaction_id" varchar
);
CREATE UNIQUE INDEX "payouts_processor_payout_id_unique" on "payouts"(
  "processor_payout_id"
);
CREATE TABLE IF NOT EXISTS "payment_line_items"(
  "id" integer primary key autoincrement not null,
  "payment_id" integer not null,
  "payable_type" varchar not null,
  "account_code" varchar not null,
  "description" varchar not null,
  "unit_amount" numeric not null,
  "tax" numeric not null,
  "total" numeric as(unit_amount + tax) stored,
  "created_at" datetime,
  "updated_at" datetime,
  "payable_id" integer,
  foreign key("payment_id") references "payments"("id") on delete cascade
);
CREATE TABLE IF NOT EXISTS "payout_line_items"(
  "id" integer primary key autoincrement not null,
  "payout_id" integer not null,
  "payment_id" integer,
  "processor_payment_id" varchar not null,
  "type" varchar not null,
  "amount" numeric not null,
  "tax" numeric not null,
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("payout_id") references "payouts"("id") on delete cascade,
  foreign key("payment_id") references "payments"("id") on delete cascade
);
CREATE TABLE IF NOT EXISTS "comments"(
  "id" integer primary key autoincrement not null,
  "user_id" integer,
  "subject_type" varchar not null,
  "subject_id" integer not null,
  "comment" text not null,
  "created_at" datetime,
  "updated_at" datetime,
  "deleted_at" datetime,
  foreign key("user_id") references "users"("id") on delete set null
);
CREATE INDEX "comments_related_type_related_id_index" on "comments"(
  "subject_type",
  "subject_id"
);
CREATE TABLE IF NOT EXISTS "warranty_products"(
  "id" integer primary key autoincrement not null,
  "cover_level_id" integer not null,
  "period" integer not null,
  "claim_limit" integer,
  "created_at" datetime,
  "updated_at" datetime,
  "deleted_at" datetime,
  "position" integer not null,
  "is_recurring" tinyint(1) not null default '0',
  foreign key("cover_level_id") references "cover_levels"("id")
);
CREATE TABLE IF NOT EXISTS "warranty_product_variants"(
  "id" integer primary key autoincrement not null,
  "product_id" integer not null,
  "max_engine_capacity" integer,
  "max_mileage" integer,
  "max_age" integer,
  "total_claim_limit" numeric not null,
  "individual_claim_limit" numeric not null,
  "provision" numeric not null,
  "selling_price" numeric not null,
  "admin_fee" numeric not null,
  "created_at" datetime,
  "updated_at" datetime,
  "monthly_selling_price" numeric,
  "monthly_admin_fee" numeric,
  foreign key("product_id") references "warranty_products"("id") on delete cascade
);
CREATE TABLE IF NOT EXISTS "invoice_line_items"(
  "id" integer primary key autoincrement not null,
  "invoice_id" integer not null,
  "sale_id" integer not null,
  "type" varchar not null,
  "account_code" varchar not null,
  "description" text not null,
  "accounting_software_id" varchar,
  "quantity" numeric not null default('1'),
  "unit_amount" numeric not null,
  "tax" numeric not null,
  "created_at" datetime,
  "updated_at" datetime,
  "total" numeric as(quantity *(unit_amount + tax)) stored,
  foreign key("sale_id") references sales("id") on delete restrict on update no action,
  foreign key("invoice_id") references invoices("id") on delete cascade on update no action
);
CREATE TABLE IF NOT EXISTS "account_user"(
  "id" integer primary key autoincrement not null,
  "account_id" integer not null,
  "user_id" integer not null,
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("account_id") references "accounts"("id") on delete cascade,
  foreign key("user_id") references "users"("id") on delete cascade
);
CREATE UNIQUE INDEX "account_user_account_id_user_id_unique" on "account_user"(
  "account_id",
  "user_id"
);
CREATE TABLE IF NOT EXISTS "warranties"(
  "id" integer primary key autoincrement not null,
  "account_id" integer not null,
  "product_id" integer not null,
  "is_self_funded" tinyint(1) not null,
  "admin_fee" numeric not null,
  "vat" numeric not null,
  "start_date" date not null,
  "end_date" date,
  "cancelled_at" datetime,
  "created_at" datetime,
  "updated_at" datetime,
  "total_claim_limit" numeric not null,
  "individual_claim_limit" numeric not null,
  "provision" numeric not null,
  "selling_price" numeric not null,
  "sales_vat" numeric not null,
  "annual_mileage_limit" integer,
  "sale_id" integer not null,
  "monthly_selling_price" numeric,
  "monthly_admin_fee" numeric,
  "monthly_provision" numeric,
  foreign key("account_id") references accounts("id") on delete no action on update no action,
  foreign key("product_id") references "warranty_products"("id") on delete no action on update no action,
  foreign key("sale_id") references sales("id") on delete cascade on update no action
);
CREATE TABLE IF NOT EXISTS "billing_requests"(
  "id" integer primary key autoincrement not null,
  "provider_customer_id" varchar,
  "provider_billing_request_id" varchar,
  "mandate_url" varchar,
  "expires_at" datetime,
  "visited_at" datetime,
  "completed_at" datetime,
  "created_at" datetime,
  "updated_at" datetime,
  "sale_id" integer,
  "mandate_id" varchar,
  "payment_id" varchar,
  "payment_confirmed_at" datetime,
  "mandate_activated_at" datetime,
  "status" varchar,
  foreign key("sale_id") references sales("id") on delete cascade on update no action
);
CREATE UNIQUE INDEX "billing_requests_sale_id_unique" on "billing_requests"(
  "sale_id"
);
CREATE TABLE IF NOT EXISTS "jobs"(
  "id" integer primary key autoincrement not null,
  "queue" varchar not null,
  "payload" text not null,
  "attempts" integer not null,
  "reserved_at" integer,
  "available_at" integer not null,
  "created_at" integer not null
);
CREATE INDEX "jobs_queue_index" on "jobs"("queue");
CREATE TABLE IF NOT EXISTS "mot_tests"(
  "id" integer primary key autoincrement not null,
  "sale_id" integer not null,
  "test_number" varchar not null,
  "completed_at" datetime not null,
  "expiry_date" date,
  "odometer_reading" integer not null,
  "odometer_unit" varchar not null,
  "result" varchar not null,
  "data_source" varchar not null,
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("sale_id") references "sales"("id") on delete cascade
);
CREATE UNIQUE INDEX "mot_tests_sale_id_test_number_unique" on "mot_tests"(
  "sale_id",
  "test_number"
);
CREATE TABLE IF NOT EXISTS "mot_test_defects"(
  "id" integer primary key autoincrement not null,
  "mot_test_id" integer not null,
  "type" varchar not null,
  "description" text not null,
  "dangerous" tinyint(1) not null,
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("mot_test_id") references "mot_tests"("id") on delete cascade
);
CREATE TABLE IF NOT EXISTS "sales_people"(
  "id" integer primary key autoincrement not null,
  "account_id" integer not null,
  "user_id" integer,
  "name" varchar not null,
  "created_at" datetime,
  "updated_at" datetime,
  "deleted_at" datetime,
  foreign key("account_id") references "accounts"("id") on delete cascade,
  foreign key("user_id") references "users"("id") on delete set null
);
CREATE UNIQUE INDEX "sales_people_account_id_name_unique" on "sales_people"(
  "account_id",
  "name"
);
CREATE TABLE IF NOT EXISTS "sales"(
  "id" integer primary key autoincrement not null,
  "account_id" integer not null,
  "dealership_id" integer not null,
  "customer_id" integer not null,
  "sold_by_id" integer not null,
  "vrm" varchar not null,
  "vin" varchar,
  "vehicle_make" varchar not null,
  "vehicle_model" varchar not null,
  "vehicle_derivative" varchar,
  "engine_capacity" integer,
  "vehicle_colour" varchar not null,
  "fuel_type" varchar not null,
  "transmission_type" varchar not null,
  "registration_date" date not null,
  "delivery_mileage" integer not null,
  "vehicle_price_paid" integer not null,
  "start_date" date not null,
  "last_service_date" date,
  "last_service_mileage" integer,
  "created_at" datetime,
  "updated_at" datetime,
  "funding_method" varchar,
  "private_plate" varchar,
  "mot_last_checked_at" datetime,
  "sales_person_id" integer,
  "confirmed_at" datetime,
  foreign key("account_id") references accounts("id") on delete no action on update no action,
  foreign key("dealership_id") references dealerships("id") on delete no action on update no action,
  foreign key("customer_id") references customers("id") on delete no action on update no action,
  foreign key("sold_by_id") references users("id") on delete no action on update no action,
  foreign key("sales_person_id") references "sales_people"("id") on delete set null
);
CREATE UNIQUE INDEX "users_email_deleted_at_unique" on "users"(
  "email",
  "deleted_at"
);
CREATE TABLE IF NOT EXISTS "dealerships"(
  "id" integer primary key autoincrement not null,
  "account_id" integer not null,
  "name" varchar not null,
  "email" varchar,
  "phone" varchar,
  "address_1" varchar,
  "address_2" varchar,
  "city" varchar,
  "county" varchar,
  "country" varchar,
  "postcode" varchar,
  "created_at" datetime,
  "updated_at" datetime,
  "deleted_at" datetime,
  "accounting_contact_id" integer,
  "billing_request_id" integer,
  "contact_first_name" varchar,
  "contact_last_name" varchar,
  foreign key("billing_request_id") references billing_requests("id") on delete set null on update no action,
  foreign key("accounting_contact_id") references accounting_contacts("id") on delete set null on update no action,
  foreign key("account_id") references "accounts"("id") on delete cascade
);
CREATE INDEX "dealerships_account_id_index" on "dealerships"("account_id");
CREATE TABLE IF NOT EXISTS "repairers"(
  "id" integer primary key autoincrement not null,
  "name" varchar not null,
  "email" varchar,
  "phone" varchar,
  "address_1" varchar,
  "address_2" varchar,
  "city" varchar,
  "county" varchar,
  "country" varchar,
  "postcode" varchar,
  "created_at" datetime,
  "updated_at" datetime,
  "deleted_at" datetime
);
CREATE INDEX "payment_line_items_payable_type_payable_id_index" on "payment_line_items"(
  "payable_type",
  "payable_id"
);
CREATE TABLE IF NOT EXISTS "service_plan_products"(
  "id" integer primary key autoincrement not null,
  "position" integer not null,
  "name" varchar not null,
  "is_pure_electric" tinyint(1) not null default '0',
  "is_recurring" tinyint(1) not null default '0',
  "created_at" datetime,
  "updated_at" datetime
);
CREATE TABLE IF NOT EXISTS "account_service_plan_products"(
  "id" integer primary key autoincrement not null,
  "account_id" integer not null,
  "service_plan_product_id" integer not null,
  "max_engine_capacity" integer,
  "duration_years" integer,
  "selling_price" numeric not null,
  "admin_fee" numeric not null,
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("account_id") references "accounts"("id"),
  foreign key("service_plan_product_id") references "service_plan_products"("id")
);
CREATE TABLE IF NOT EXISTS "payments"(
  "id" integer primary key autoincrement not null,
  "billing_request_id" integer,
  "payout_id" integer,
  "period_start" date,
  "amount" numeric,
  "amount_refunded" numeric not null default('0'),
  "processor_payment_id" varchar,
  "status" varchar,
  "charge_date" date,
  "created_at" datetime,
  "updated_at" datetime,
  "invoice_id" integer,
  "accounting_software_bank_transfer_id" varchar,
  foreign key("invoice_id") references invoices("id") on delete no action on update no action,
  foreign key("payout_id") references payouts("id") on delete set null on update no action,
  foreign key("billing_request_id") references billing_requests("id") on delete cascade on update no action
);
CREATE UNIQUE INDEX "payments_processor_payment_id_unique" on "payments"(
  "processor_payment_id"
);
CREATE TABLE IF NOT EXISTS "claim_authorisations"(
  "id" integer primary key autoincrement not null,
  "estimate_id" integer not null,
  "user_id" integer not null,
  "work_done" text not null,
  "total_parts" numeric not null,
  "total_labour" numeric not null,
  "authorised_gross" numeric,
  "settled_at" datetime,
  "created_at" datetime,
  "updated_at" datetime,
  "vat" numeric not null,
  "authorised_net" numeric,
  "invoice_id" integer,
  "customer_contribution" numeric,
  "reason" varchar,
  foreign key("invoice_id") references invoices("id") on delete set null on update no action,
  foreign key("user_id") references users("id") on delete no action on update no action,
  foreign key("estimate_id") references claim_estimates("id") on delete no action on update no action
);
CREATE UNIQUE INDEX "claim_authorisations_estimate_id_unique" on "claim_authorisations"(
  "estimate_id"
);
CREATE TABLE IF NOT EXISTS "breakdown_plans"(
  "id" integer primary key autoincrement not null,
  "account_id" integer not null,
  "sale_id" integer not null,
  "breakdown_product_id" integer not null,
  "provision" numeric not null,
  "selling_price" numeric not null,
  "admin_fee" numeric not null,
  "vat" numeric not null,
  "end_date" date not null,
  "created_at" datetime,
  "updated_at" datetime,
  "is_self_funded" tinyint(1) not null,
  "sales_vat" numeric not null,
  "cancelled_at" datetime,
  foreign key("account_id") references accounts("id") on delete no action on update no action,
  foreign key("breakdown_product_id") references breakdown_products("id") on delete no action on update no action,
  foreign key("sale_id") references "sales"("id") on delete cascade
);
CREATE TABLE IF NOT EXISTS "product_groups"(
  "id" integer primary key autoincrement not null,
  "name" varchar not null,
  "created_at" datetime,
  "updated_at" datetime,
  "deleted_at" datetime
);
CREATE TABLE IF NOT EXISTS "products"(
  "id" integer primary key autoincrement not null,
  "product_group_id" integer not null,
  "name" varchar not null,
  "cost_price" numeric,
  "selling_price" numeric,
  "dealer_commission" numeric,
  "created_at" datetime,
  "updated_at" datetime,
  "deleted_at" datetime,
  foreign key("product_group_id") references "product_groups"("id") on delete cascade
);
CREATE TABLE IF NOT EXISTS "account_products"(
  "id" integer primary key autoincrement not null,
  "account_id" integer not null,
  "product_id" integer not null,
  "cost_price" numeric,
  "selling_price" numeric,
  "dealer_commission" numeric,
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("account_id") references "accounts"("id") on delete cascade,
  foreign key("product_id") references "products"("id") on delete cascade
);
CREATE TABLE IF NOT EXISTS "sale_products"(
  "id" integer primary key autoincrement not null,
  "sale_id" integer not null,
  "product_id" integer not null,
  "name" varchar not null,
  "cost_price" numeric,
  "selling_price" numeric,
  "dealer_commission" numeric,
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("sale_id") references "sales"("id") on delete cascade,
  foreign key("product_id") references "products"("id")
);
CREATE TABLE IF NOT EXISTS "documents"(
  "id" integer primary key autoincrement not null,
  "slug" varchar not null,
  "title" varchar not null,
  "description" text,
  "created_at" datetime,
  "updated_at" datetime
);
CREATE UNIQUE INDEX "documents_slug_unique" on "documents"("slug");
CREATE UNIQUE INDEX "documents_title_unique" on "documents"("title");
CREATE TABLE IF NOT EXISTS "document_versions"(
  "id" integer primary key autoincrement not null,
  "document_id" integer not null,
  "version_number" integer not null,
  "change_notes" text,
  "content" text not null,
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("document_id") references "documents"("id") on delete cascade
);
CREATE TABLE IF NOT EXISTS "service_plans"(
  "id" integer primary key autoincrement not null,
  "account_id" integer not null,
  "sale_id" integer not null,
  "service_plan_product_id" integer not null,
  "duration_years" integer,
  "end_date" date,
  "selling_price" numeric not null,
  "admin_fee" numeric not null,
  "vat" numeric not null,
  "cancelled_at" datetime,
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("service_plan_product_id") references service_plan_products("id") on delete no action on update no action,
  foreign key("sale_id") references sales("id") on delete cascade on update no action,
  foreign key("account_id") references accounts("id") on delete no action on update no action
);
CREATE TABLE IF NOT EXISTS "notifications"(
  "id" varchar not null,
  "type" varchar not null,
  "notifiable_type" varchar not null,
  "notifiable_id" integer not null,
  "data" text not null,
  "read_at" datetime,
  "created_at" datetime,
  "updated_at" datetime,
  primary key("id")
);
CREATE INDEX "notifications_notifiable_type_notifiable_id_index" on "notifications"(
  "notifiable_type",
  "notifiable_id"
);
CREATE TABLE IF NOT EXISTS "one_time_passcodes"(
  "id" integer primary key autoincrement not null,
  "otp_authenticatable_type" varchar not null,
  "otp_authenticatable_id" integer not null,
  "passcode" varchar not null,
  "expires_at" datetime not null,
  "created_at" datetime,
  "updated_at" datetime
);
CREATE INDEX "otp_index" on "one_time_passcodes"(
  "otp_authenticatable_type",
  "otp_authenticatable_id"
);
CREATE TABLE IF NOT EXISTS "claims"(
  "id" integer primary key autoincrement not null,
  "warranty_id" integer not null,
  "fault_type_id" integer not null,
  "entered_by_id" integer,
  "reference" varchar not null,
  "failure_date" date not null,
  "current_mileage" integer,
  "vehicle_location" text not null,
  "fault_description" text not null,
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("warranty_id") references warranties("id") on delete no action on update no action,
  foreign key("fault_type_id") references fault_types("id") on delete no action on update no action,
  foreign key("entered_by_id") references users("id") on delete no action on update no action
);
CREATE TABLE IF NOT EXISTS "claim_estimates"(
  "id" integer primary key autoincrement not null,
  "claim_id" integer not null,
  "user_id" integer,
  "workshop_name" varchar not null,
  "workshop_contact" varchar,
  "workshop_phone" varchar,
  "workshop_email" varchar,
  "workshop_address" text,
  "work_required" text,
  "total_parts" numeric,
  "total_labour" numeric,
  "vat" numeric,
  "customer_contribution" numeric,
  "invoicing_dealer_direct" tinyint(1) not null default('0'),
  "vat_charged" tinyint(1) not null default('1'),
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("claim_id") references claims("id") on delete no action on update no action,
  foreign key("user_id") references users("id") on delete no action on update no action
);
CREATE TABLE IF NOT EXISTS "account_warranty_breakdown_product_bundles"(
  "id" integer primary key autoincrement not null,
  "account_warranty_product_id" integer not null,
  "breakdown_product_id" integer not null,
  "provision" numeric not null,
  "admin_fee" numeric not null,
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("account_warranty_product_id") references "account_warranty_products"("id"),
  foreign key("breakdown_product_id") references "breakdown_products"("id")
);
CREATE TABLE IF NOT EXISTS "service_plan_redemptions"(
  "id" integer primary key autoincrement not null,
  "service_plan_id" integer not null,
  "amount_net" numeric not null,
  "vat" numeric not null,
  "amount_gross" numeric not null,
  "is_service" tinyint(1) not null default '0',
  "is_mot" tinyint(1) not null default '0',
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("service_plan_id") references "service_plans"("id") on delete cascade
);
CREATE TABLE IF NOT EXISTS "ai_tasks"(
  "id" integer primary key autoincrement not null,
  "name" varchar not null,
  "processor_class" varchar not null,
  "created_at" datetime,
  "updated_at" datetime
);
CREATE TABLE IF NOT EXISTS "ai_prompts"(
  "id" integer primary key autoincrement not null,
  "ai_task_id" integer not null,
  "version_number" integer not null,
  "model" varchar not null,
  "change_notes" text,
  "prompt" text not null,
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("ai_task_id") references "ai_tasks"("id") on delete cascade
);
CREATE TABLE IF NOT EXISTS "ai_requests"(
  "id" integer primary key autoincrement not null,
  "ai_prompt_id" integer not null,
  "subject_type" varchar not null,
  "subject_id" integer not null,
  "completion_model" varchar,
  "finish_reason" varchar,
  "response" text,
  "prompt_tokens" integer,
  "completion_tokens" integer,
  "processing_ms" integer,
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("ai_prompt_id") references "ai_prompts"("id") on delete cascade
);
CREATE INDEX "ai_requests_subject_type_subject_id_index" on "ai_requests"(
  "subject_type",
  "subject_id"
);
CREATE TABLE IF NOT EXISTS "ai_votes"(
  "id" integer primary key autoincrement not null,
  "ai_request_id" integer not null,
  "user_id" integer not null,
  "rating" integer not null,
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("ai_request_id") references "ai_requests"("id") on delete cascade,
  foreign key("user_id") references "users"("id")
);
CREATE TABLE IF NOT EXISTS "ai_request_scores"(
  "id" integer primary key autoincrement not null,
  "ai_request_id" integer not null,
  "rating" integer not null,
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("ai_request_id") references "ai_requests"("id") on delete cascade
);
CREATE UNIQUE INDEX "ai_request_scores_ai_request_id_unique" on "ai_request_scores"(
  "ai_request_id"
);
CREATE TABLE IF NOT EXISTS "cover_levels"(
  "id" integer primary key autoincrement not null,
  "name" varchar not null,
  "created_at" datetime,
  "updated_at" datetime,
  "vehicle_type" varchar,
  "document_id" integer,
  foreign key("document_id") references "documents"("id") on delete set null
);
CREATE UNIQUE INDEX "cover_levels_name_unique" on "cover_levels"("name");

INSERT INTO migrations VALUES(1,'2020_01_01_000001_create_password_resets_table',1);
INSERT INTO migrations VALUES(2,'2020_01_01_000002_create_failed_jobs_table',1);
INSERT INTO migrations VALUES(3,'2020_01_01_000003_create_accounts_table',1);
INSERT INTO migrations VALUES(4,'2020_01_01_000004_create_dealerships_table',1);
INSERT INTO migrations VALUES(5,'2020_01_01_000005_create_users_table',1);
INSERT INTO migrations VALUES(6,'2021_11_05_130030_create_cover_levels_table',1);
INSERT INTO migrations VALUES(7,'2021_11_05_134159_create_products_table',1);
INSERT INTO migrations VALUES(8,'2021_11_05_135109_create_account_product_table',1);
INSERT INTO migrations VALUES(9,'2021_11_08_143320_create_permission_tables',1);
INSERT INTO migrations VALUES(10,'2021_11_09_080506_create_fault_types_table',1);
INSERT INTO migrations VALUES(11,'2021_11_10_145257_create_warranties_table',1);
INSERT INTO migrations VALUES(12,'2021_11_10_153442_create_claims_table',1);
INSERT INTO migrations VALUES(13,'2021_11_10_153810_create_claim_estimates_table',1);
INSERT INTO migrations VALUES(14,'2022_01_13_133542_create_claim_authorisations_table',1);
INSERT INTO migrations VALUES(15,'2022_04_05_082756_alter_products_add_extra_price_columns',1);
INSERT INTO migrations VALUES(16,'2022_04_05_091645_alter_account_product_add_extra_price_columns',1);
INSERT INTO migrations VALUES(17,'2022_04_05_103215_alter_warranties_table_add_extra_price_columns',1);
INSERT INTO migrations VALUES(18,'2022_04_05_114756_alter_warranties_add_last_service_columns',1);
INSERT INTO migrations VALUES(19,'2022_04_05_144044_create_user_invites_table',1);
INSERT INTO migrations VALUES(20,'2022_04_28_183119_rename_cover_levels',1);
INSERT INTO migrations VALUES(21,'2022_05_05_092352_alter_products_setup_vehicle_based_pricing_tiers',1);
INSERT INTO migrations VALUES(22,'2022_05_05_092610_create_product_variants_table',1);
INSERT INTO migrations VALUES(23,'2022_05_05_125106_alter_account_product_table_add_limit_columns',1);
INSERT INTO migrations VALUES(24,'2022_05_26_115612_alter_claims_remove_unused_approved_at_column',1);
INSERT INTO migrations VALUES(25,'2022_05_26_115712_alter_claims_remove_unused_status_column',1);
INSERT INTO migrations VALUES(26,'2022_05_26_115812_alter_claim_estimates_remove_unused_approved_at_column',1);
INSERT INTO migrations VALUES(27,'2022_05_30_092842_create_files_table',1);
INSERT INTO migrations VALUES(28,'2022_06_20_172507_alter_accounts_table_add_breakdown_self_funded_column',1);
INSERT INTO migrations VALUES(29,'2022_06_20_173225_create_breakdown_products_table',1);
INSERT INTO migrations VALUES(30,'2022_06_20_173423_create_account_breakdown_product_table',1);
INSERT INTO migrations VALUES(31,'2022_06_21_102022_create_breakdown_policies_table',1);
INSERT INTO migrations VALUES(32,'2022_06_24_132841_create_breakdown_claims_table',1);
INSERT INTO migrations VALUES(33,'2022_06_29_111004_alter_breakdown_policies_table_add_self_funded_column',1);
INSERT INTO migrations VALUES(34,'2022_07_05_104302_alter_claim_estimates_table_add_vat_column',1);
INSERT INTO migrations VALUES(35,'2022_07_05_124809_alter_claim_authorisations_table_add_vat_column',1);
INSERT INTO migrations VALUES(36,'2022_07_06_105309_create_repairers_table',1);
INSERT INTO migrations VALUES(37,'2022_07_06_134548_create_claim_rejections_table',1);
INSERT INTO migrations VALUES(38,'2022_07_07_083956_alter_warranties_table_add_sales_vat_column',1);
INSERT INTO migrations VALUES(39,'2022_07_07_084056_alter_breakdown_policies_table_add_sales_vat_column',1);
INSERT INTO migrations VALUES(40,'2022_07_07_084912_alter_claims_table_drop_unused_columns',1);
INSERT INTO migrations VALUES(41,'2022_07_07_085110_alter_claim_estimates_table_increase_decimal_sizes',1);
INSERT INTO migrations VALUES(42,'2022_07_07_085210_alter_claim_authorisations_table_increase_decimal_sizes',1);
INSERT INTO migrations VALUES(43,'2022_11_16_144535_alter_breakdown_claims_add_vat_column',1);
INSERT INTO migrations VALUES(44,'2022_11_16_170310_alter_account_product_table_add_annual_mileage_limit',1);
INSERT INTO migrations VALUES(45,'2022_11_16_170712_alter_warranties_table_add_annual_mileage_limit',1);
INSERT INTO migrations VALUES(46,'2022_11_18_120442_alter_claim_authorisations_add_gross_net_amounts',1);
INSERT INTO migrations VALUES(47,'2022_11_18_123616_alter_warranties_table_remove_paid_at_column',1);
INSERT INTO migrations VALUES(48,'2023_06_17_151259_alter_users_table_make_account_id_nullable',1);
INSERT INTO migrations VALUES(49,'2023_06_21_094639_create_accounting_contacts_table',1);
INSERT INTO migrations VALUES(50,'2023_06_21_220537_alter_dealerships_add_accounting_software_id',1);
INSERT INTO migrations VALUES(51,'2023_06_21_222146_create_invoices_table',1);
INSERT INTO migrations VALUES(52,'2023_06_21_222605_create_invoice_line_items_table',1);
INSERT INTO migrations VALUES(53,'2023_06_23_155103_alter_breakdown_policies_remove_cancelled_at_column',1);
INSERT INTO migrations VALUES(54,'2023_07_03_092616_delete_blank_invoice',1);
INSERT INTO migrations VALUES(55,'2023_07_11_123517_create_customers_table',1);
INSERT INTO migrations VALUES(56,'2023_07_11_123525_create_sales_table',1);
INSERT INTO migrations VALUES(57,'2023_07_11_131210_drop_normalised_columns_from_warranties_table',1);
INSERT INTO migrations VALUES(58,'2023_07_11_164046_alter_breakdown_policies_add_policy_id_column',1);
INSERT INTO migrations VALUES(59,'2023_07_13_094502_rename_breakdown_policies_to_breakdown_plans',1);
INSERT INTO migrations VALUES(60,'2023_07_13_144339_alter_claim_authorisations_table_add_invoice_id_column',1);
INSERT INTO migrations VALUES(61,'2023_07_13_144836_alter_invoices_table_make_period_start_period_end_nullable',1);
INSERT INTO migrations VALUES(62,'2023_07_17_101235_alter_invoice_line_items_table_drop_warranty_id_add_sale_id',1);
INSERT INTO migrations VALUES(63,'2023_11_16_151726_alter_product_variants_table_add_monthly_columns',1);
INSERT INTO migrations VALUES(64,'2023_11_16_152626_alter_account_product_table_add_monthly_columns',1);
INSERT INTO migrations VALUES(65,'2023_11_16_160705_alter_warranties_table_add_monthly_columns',1);
INSERT INTO migrations VALUES(66,'2023_11_17_091302_alter_invoices_table_change_dealership_id_to_morphs',1);
INSERT INTO migrations VALUES(67,'2023_11_17_102804_alter_customers_table_add_accounting_software_id_column',1);
INSERT INTO migrations VALUES(68,'2023_11_20_142024_create_customer_billing_requests_table',1);
INSERT INTO migrations VALUES(69,'2023_11_29_091130_delete_unsynced_invoice_line_items',1);
INSERT INTO migrations VALUES(70,'2023_12_13_133354_alter_customer_billing_requests_make_provider_customer_id_nullable',1);
INSERT INTO migrations VALUES(71,'2024_01_22_094141_rename_customer_billing_requests_table',1);
INSERT INTO migrations VALUES(72,'2024_01_22_094637_alter_billing_requests_add_sale_relation_remove_customer_relation',1);
INSERT INTO migrations VALUES(73,'2024_02_02_163712_alter_invoices_table_add_gocardless_payment_id_column',1);
INSERT INTO migrations VALUES(74,'2024_03_15_132140_create_payouts_table',1);
INSERT INTO migrations VALUES(75,'2024_03_15_133532_create_payments_table',1);
INSERT INTO migrations VALUES(76,'2024_03_16_083224_alter_payouts_table_add_accounting_software_id',1);
INSERT INTO migrations VALUES(77,'2024_03_16_094934_create_payment_line_items_table',1);
INSERT INTO migrations VALUES(78,'2024_03_18_095637_create_payout_line_items_table',1);
INSERT INTO migrations VALUES(79,'2024_04_05_134051_alter_billing_requests_add_mandate_activated_at_timestamp',1);
INSERT INTO migrations VALUES(80,'2024_05_02_125953_alter_accounts_add_send_contract_emails_boolean',1);
INSERT INTO migrations VALUES(81,'2024_05_02_132825_alter_claim_estimates_table_make_some_fields_nullable',1);
INSERT INTO migrations VALUES(82,'2024_05_02_134654_alter_sales_table_add_funding_method',1);
INSERT INTO migrations VALUES(83,'2024_05_02_140253_alter_claim_estimates_table_add_customer_contribution',1);
INSERT INTO migrations VALUES(84,'2024_05_02_140553_alter_claim_authorisations_table_add_customer_contribution',1);
INSERT INTO migrations VALUES(85,'2024_05_03_105153_alter_invoice_line_items_allow_negative_values',1);
INSERT INTO migrations VALUES(86,'2024_05_03_113944_create_comments_table',1);
INSERT INTO migrations VALUES(87,'2024_05_15_102320_alter_claims_make_current_mileage_nullable',1);
INSERT INTO migrations VALUES(88,'2024_05_15_133411_alter_claim_authorisations_add_reason_column',1);
INSERT INTO migrations VALUES(89,'2024_05_15_141532_alter_accounts_table_add_claim_handling_notes',1);
INSERT INTO migrations VALUES(90,'2024_05_15_144144_alter_products_remove_unique_constraint',1);
INSERT INTO migrations VALUES(91,'2024_05_15_144144_alter_products_table_add_is_recurring_column',1);
INSERT INTO migrations VALUES(92,'2024_05_15_160835_alter_sales_add_private_plate_column',1);
INSERT INTO migrations VALUES(93,'2024_05_23_144401_alter_invoices_table_add_reference',1);
INSERT INTO migrations VALUES(94,'2024_05_23_193954_alter_product_variants_add_foreign_key_constraint',1);
INSERT INTO migrations VALUES(95,'2024_05_30_165347_alter_invoice_line_items_make_description_unlimited_length',1);
INSERT INTO migrations VALUES(96,'2024_06_05_143307_create_account_user_pivot_table',1);
INSERT INTO migrations VALUES(97,'2024_06_06_103215_alter_cover_levels_add_vehicle_type_column',1);
INSERT INTO migrations VALUES(98,'2024_06_10_114138_alter_sales_make_engine_capacity_nullable',1);
INSERT INTO migrations VALUES(99,'2024_07_10_142958_alter_claim_estimates_add_invoicing_dealer_direct_boolean',1);
INSERT INTO migrations VALUES(100,'2024_07_10_151858_alter_claim_estimates_add_vat_charged_boolean',1);
INSERT INTO migrations VALUES(101,'2024_08_23_073525_cancel_andrews_monthly_warranties',1);
INSERT INTO migrations VALUES(102,'2024_08_23_085433_alter_warranties_allow_null_end_date',1);
INSERT INTO migrations VALUES(103,'2024_09_20_084530_alter_dealerships_add_billing_columns',1);
INSERT INTO migrations VALUES(104,'2024_09_20_130813_alter_payments_make_fields_nullable',1);
INSERT INTO migrations VALUES(105,'2024_09_20_131237_alter_payments_add_invoice_id_column',1);
INSERT INTO migrations VALUES(106,'2024_09_20_170039_alter_billing_requests_add_status_column',1);
INSERT INTO migrations VALUES(107,'2024_10_01_114052_create_jobs_table',1);
INSERT INTO migrations VALUES(108,'2024_10_01_124408_seed_roles_and_permissions',1);
INSERT INTO migrations VALUES(109,'2024_10_02_104400_create_mot_tests_table',1);
INSERT INTO migrations VALUES(110,'2024_10_02_104600_create_mot_test_defects_table',1);
INSERT INTO migrations VALUES(111,'2024_10_02_111905_alter_sales_add_mot_last_checked_at_column',1);
INSERT INTO migrations VALUES(112,'2024_10_02_121933_create_sales_people_table',1);
INSERT INTO migrations VALUES(113,'2024_10_02_124859_alter_sales_add_sales_person_id',1);
INSERT INTO migrations VALUES(114,'2024_10_02_141308_seed_roles_and_permissions',1);
INSERT INTO migrations VALUES(115,'2024_10_03_101546_alter_users_table_make_email_unique_composite',1);
INSERT INTO migrations VALUES(116,'2024_10_03_153116_alter_dealerships_add_account_id_foreign_key',1);
INSERT INTO migrations VALUES(117,'2024_10_03_153216_alter_repairers_add_account_id_foreign_key',1);
INSERT INTO migrations VALUES(118,'2024_10_22_000000_rename_password_resets_table',1);
INSERT INTO migrations VALUES(119,'2024_10_25_095945_alter_invoices_add_payment_accounting_software_id',1);
INSERT INTO migrations VALUES(120,'2024_10_28_085642_alter_payouts_add_from_to_bank_transaction_payment_ids',1);
INSERT INTO migrations VALUES(121,'2024_10_28_100635_alter_payments_add_accounting_software_bank_transfer_id',1);
INSERT INTO migrations VALUES(122,'2024_10_31_152331_alter_payment_line_items_add_payable_morph_columns',1);
INSERT INTO migrations VALUES(123,'2024_11_13_142239_create_service_plan_products_table',1);
INSERT INTO migrations VALUES(124,'2024_11_13_143323_create_account_service_plan_product_table',1);
INSERT INTO migrations VALUES(125,'2024_11_15_112005_create_service_plans_table',1);
INSERT INTO migrations VALUES(126,'2024_11_29_133723_alter_payments_make_amount_nullable',1);
INSERT INTO migrations VALUES(127,'2024_11_29_143450_update_monthly_warranties_add_vat',1);
INSERT INTO migrations VALUES(128,'2025_01_01_130556_enable_maintenance_mode',1);
INSERT INTO migrations VALUES(129,'2025_01_13_101632_alter_sales_table_add_confirmed_at_column',1);
INSERT INTO migrations VALUES(130,'2025_01_16_104703_alter_claim_estimates_set_customer_contribution_nullable',1);
INSERT INTO migrations VALUES(131,'2025_01_16_104803_alter_claim_authorisations_set_customer_contribution_nullable',1);
INSERT INTO migrations VALUES(132,'2025_01_16_161750_alter_breakdown_plans_make_sale_id_cascade',1);
INSERT INTO migrations VALUES(133,'2025_01_17_092527_rename_comments_table_columns',1);
INSERT INTO migrations VALUES(134,'2025_01_20_163253_rename_products_to_warranty_products',1);
INSERT INTO migrations VALUES(135,'2025_01_20_163353_rename_product_variants_to_warranty_product_variants',1);
INSERT INTO migrations VALUES(136,'2025_01_20_163453_rename_account_product_to_account_warranty_product',1);
INSERT INTO migrations VALUES(137,'2025_01_20_163553_rename_account_breakdown_product_to_account_breakdown_products',1);
INSERT INTO migrations VALUES(138,'2025_01_20_163653_rename_account_service_plan_product_to_account_service_plan_products',1);
INSERT INTO migrations VALUES(139,'2025_01_20_164653_create_product_groups_table',1);
INSERT INTO migrations VALUES(140,'2025_01_20_164853_create_products_table',1);
INSERT INTO migrations VALUES(141,'2025_01_20_180400_create_account_products_table',1);
INSERT INTO migrations VALUES(142,'2025_01_21_103446_create_sale_products_table',1);
INSERT INTO migrations VALUES(143,'2025_01_21_130825_create_documents_table',1);
INSERT INTO migrations VALUES(144,'2025_01_21_135430_create_document_versions_table',1);
INSERT INTO migrations VALUES(145,'2025_01_21_164914_alter_service_plans_make_end_date_a_date',1);
INSERT INTO migrations VALUES(146,'2025_01_22_083701_create_warranty_burn_rates_view',1);
INSERT INTO migrations VALUES(147,'2025_01_27_132324_create_notifications_table',1);
INSERT INTO migrations VALUES(148,'2025_01_28_164754_create_one_time_passcodes_table',1);
INSERT INTO migrations VALUES(149,'2025_01_29_160210_alter_claims_make_entered_by_id_nullable',1);
INSERT INTO migrations VALUES(150,'2025_01_29_160230_alter_claim_estimates_make_user_id_nullable',1);
INSERT INTO migrations VALUES(151,'2025_02_03_194808_create_account_warranty_breakdown_product_bundles_table',1);
INSERT INTO migrations VALUES(152,'2025_02_04_160056_create_service_plan_redemptions_table',1);
INSERT INTO migrations VALUES(153,'2025_02_05_072812_create_ai_tasks_table',1);
INSERT INTO migrations VALUES(154,'2025_02_05_072939_create_ai_prompts_table',1);
INSERT INTO migrations VALUES(155,'2025_02_05_073340_create_ai_requests_table',1);
INSERT INTO migrations VALUES(156,'2025_02_05_155101_create_ai_votes_table',1);
INSERT INTO migrations VALUES(157,'2025_02_06_114528_create_ai_request_scores_table',1);
INSERT INTO migrations VALUES(158,'2025_02_07_124649_alter_cover_levels_add_document_id_column',1);
