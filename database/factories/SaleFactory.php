<?php

namespace Database\Factories;

use App\Enums\VehicleType;
use App\Models\Account;
use App\Models\BillingRequest;
use App\Models\Customer;
use App\Models\Dealership;
use App\Models\PayLaterAgreement;
use App\Models\User;
use App\Models\Warranty;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Sale>
 */
class SaleFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'account_id' => Account::factory(),
            'dealership_id' => Dealership::factory(),
            'customer_id' => Customer::factory(),
            'sold_by_id' => User::factory(),

            'vrm' => strtoupper($this->faker->bothify('??## ???')),
            'vin' => strtoupper($this->faker->unique()->bothify('?????????????????')),
            'vehicle_make' => 'Audi',
            'vehicle_model' => 'A3',
            'vehicle_derivative' => 'Sportback',
            'engine_capacity' => 2000,
            'vehicle_colour' => 'Black',
            'vehicle_type' => VehicleType::CAR,
            'fuel_type' => 'Petrol',
            'transmission_type' => 'Manual',
            'registration_date' => $this->faker->dateTimeBetween('-6 years', '-1 year'),
            'delivery_mileage' => 40000,
            'vehicle_price_paid' => 20000,
            'funding_method' => 'Cash',
            'start_date' => now(),
        ];
    }

    public function confirmed(?Account $account = null): Factory
    {
        return $this
            ->recycle($account ?: Account::factory()->create())
            ->state([
                'confirmed_at' => now(),
            ]);
    }

    public function confirmedWithProducts()
    {
        return $this->confirmed()
            ->hasWarranty()
            ->hasBreakdownPlan()
            ->hasServicePlan();
    }

    public function subscription()
    {
        return $this->confirmed()
            ->has(Warranty::factory()->subscription());
    }

    public function pending(): Factory
    {
        return $this->state([
            'start_date' => now()->addDay(),
        ]);
    }

    public function preApprovedForPayLater(bool $approved = true)
    {
        return $this
            ->has(PayLaterAgreement::factory()->approved($approved));
    }

    public function withPayLaterAgreementSelected()
    {
        return $this
            ->has(PayLaterAgreement::factory()->approvedAndPlanSelected());
    }

    public function withPayLaterAgreementCompleted()
    {
        return $this
            ->has(PayLaterAgreement::factory()->completed());
    }

    public function withDirectDebitMandatePending()
    {
        return $this->for(BillingRequest::factory()->pending());
    }

    public function withDirectDebitMandateCompleted()
    {
        return $this->for(BillingRequest::factory()->completed());
    }
}
