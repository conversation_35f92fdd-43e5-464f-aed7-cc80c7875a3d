<?php

namespace Database\Factories;

use App\Enums\VehicleType;
use App\Models\Account;
use App\Models\Customer;
use App\Models\Dealership;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Sale>
 */
class SaleFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'account_id' => Account::factory(),
            'dealership_id' => Dealership::factory(),
            'customer_id' => Customer::factory(),
            'sold_by_id' => User::factory(),

            'vrm' => strtoupper($this->faker->bothify('??## ???')),
            'vin' => strtoupper($this->faker->unique()->bothify('?????????????????')),
            'vehicle_make' => 'Audi',
            'vehicle_model' => 'A3',
            'vehicle_derivative' => 'Sportback',
            'engine_capacity' => 2000,
            'vehicle_colour' => 'Black',
            'vehicle_type' => VehicleType::CAR,
            'fuel_type' => 'Petrol',
            'transmission_type' => 'Manual',
            'registration_date' => $this->faker->dateTimeBetween('-6 years', '-1 year'),
            'delivery_mileage' => 40000,
            'vehicle_price_paid' => 20000,
            'funding_method' => 'Cash',
            'start_date' => now(),
        ];
    }

    public function confirmed(): Factory
    {
        return $this->state([
            'confirmed_at' => now(),
        ]);
    }

    public function pending(): Factory
    {
        return $this->state([
            'start_date' => now()->addDay(),
        ]);
    }
}
