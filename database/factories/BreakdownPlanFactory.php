<?php

namespace Database\Factories;

use App\Models\Account;
use App\Models\AccountBreakdownProduct;
use App\Models\Sale;
use Illuminate\Database\Eloquent\Factories\Factory;

class BreakdownPlanFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'account_id' => Account::factory(),
            'sale_id' => Sale::factory(),
            'breakdown_product_id' => AccountBreakdownProduct::factory(),
            'is_self_funded' => false,
            'provision' => 0,
            'admin_fee' => 0,
            'selling_price' => 0,
            'vat' => 0,
            'sales_vat' => 0,
            'end_date' => now()->addYear()->subDay(),
        ];
    }
}
