<?php

namespace Database\Factories;

use App\Models\AccountServiceCredentials;
use App\Models\PayLaterPlan;
use Illuminate\Database\Eloquent\Factories\Factory;

class AccountFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->company(),
            'authorisation_contact' => $this->faker->name(),
            'warranty_self_funded' => false,
            'breakdown_self_funded' => false,
            'send_contract_emails' => true,
            'max_hourly_labour_rate' => 50,
        ];
    }

    public function notSendingContractEmails()
    {
        return $this->state([
            'send_contract_emails' => false,
        ]);
    }

    public function withPayLater()
    {
        return $this
            ->has(AccountServiceCredentials::factory()->paymentAssist(), 'serviceCredentials')
            ->has(PayLaterPlan::factory()->state([
                'commission_rate_margin' => 1.23,
                'deposit' => true,
            ]));
    }
}
