<?php

use App\Models\Dealership;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('dealerships', function (Blueprint $table) {
            $table->string('short_name')->nullable()->after('name');
        });

        Dealership::query()->withTrashed()->each(function (Dealership $dealership) {
            $shortName = Str::shortName($dealership->name);

            if (strlen($shortName) > 10) {
                $shortName = substr($shortName, 0, 10);
            }

            $dealership->forceFill([
                'short_name' => $shortName,
            ])->save();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('dealerships', function (Blueprint $table) {
            $table->dropColumn('short_name');
        });
    }
};
