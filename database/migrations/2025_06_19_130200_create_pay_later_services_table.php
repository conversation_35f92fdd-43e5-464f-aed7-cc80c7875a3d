<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pay_later_services', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(\App\Models\Account::class)->constrained();
            $table->foreignIdFor(\App\Models\Customer::class)->constrained()->cascadeOnDelete();

            $table->string('vrm');
            $table->string('private_plate')->nullable();
            $table->string('vin')->nullable();
            $table->string('vehicle_type')->nullable();
            $table->string('vehicle_make');
            $table->string('vehicle_model');
            $table->string('vehicle_derivative')->nullable();
            $table->unsignedInteger('engine_capacity')->nullable();
            $table->string('vehicle_colour');
            $table->string('fuel_type');
            $table->string('transmission_type');
            $table->date('registration_date');

            $table->decimal('invoice_amount', 8, 2)->unsigned()->nullable();
            $table->decimal('deposit_amount', 8, 2)->unsigned()->nullable();
            $table->decimal('dealer_amount', 8, 2)->unsigned()->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pay_later_services');
    }
};
