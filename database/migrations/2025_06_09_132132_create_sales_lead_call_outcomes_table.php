<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sales_lead_call_outcomes', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(\App\Models\SalesLead::class)->constrained();
            $table->foreignIdFor(\App\Models\PhoneCall::class)->nullable()->constrained()->nullOnDelete();
            $table->string('outcome');
            $table->dateTime('callback_date')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sales_lead_call_outcomes');
    }
};
