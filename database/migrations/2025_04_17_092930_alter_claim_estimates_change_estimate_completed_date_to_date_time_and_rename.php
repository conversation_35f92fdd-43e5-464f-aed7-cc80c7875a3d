<?php

use App\Models\ClaimEstimate;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('claim_estimates', function (Blueprint $table) {
            $table->timestamp('estimate_completed_at')->nullable()->after('estimate_completed_date');
        });
        ClaimEstimate::query()->update(['estimate_completed_at' => DB::raw('estimate_completed_date')]);
        Schema::table('claim_estimates', function (Blueprint $table) {
            $table->dropColumn('estimate_completed_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('claim_estimates', function (Blueprint $table) {
            $table->date('estimate_completed_date')->nullable()->after('estimate_completed_at');
        });
        ClaimEstimate::query()->update(['estimate_completed_date' => DB::raw('estimate_completed_at')]);
        Schema::table('claim_estimates', function (Blueprint $table) {
            $table->dropColumn('estimate_completed_at');
        });
    }
};
