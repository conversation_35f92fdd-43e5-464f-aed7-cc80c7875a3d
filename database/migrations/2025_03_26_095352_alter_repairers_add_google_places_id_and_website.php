<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('repairers', function (Blueprint $table) {
            if (Schema::hasColumn('repairers', 'account_id')) {
                $table->dropConstrainedForeignId('account_id');
            }

            $table->string('website')->nullable()->after('phone');
            $table->string('place_id')->nullable()->after('postcode');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('repairers', function (Blueprint $table) {
            $table->dropColumn('website');
            $table->dropColumn('place_id');
        });
    }
};
