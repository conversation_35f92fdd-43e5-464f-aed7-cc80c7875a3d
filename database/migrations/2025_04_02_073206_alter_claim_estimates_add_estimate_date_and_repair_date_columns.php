<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('claim_estimates', function (Blueprint $table) {
            $table->date('estimate_booking_date')->nullable()->after('workshop_address');
            $table->date('estimate_completed_date')->nullable()->after('estimate_booking_date');
            $table->date('repair_date')->nullable()->after('estimate_completed_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('claim_estimates', function (Blueprint $table) {
            $table->dropColumn('estimate_date');
            $table->dropColumn('repair_date');
        });
    }
};
