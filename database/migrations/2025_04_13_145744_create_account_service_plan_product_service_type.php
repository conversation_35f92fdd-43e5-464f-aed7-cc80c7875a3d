<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('account_service_plan_product_service_type', function (Blueprint $table) {
            $table->id();
            $table->foreignId('account_service_plan_product_id')->constrained(indexName: 'account_service_plan_product_foreign')->cascadeOnDelete();
            $table->foreignId('service_type_id')->constrained(indexName: 'service_type_foreign')->cascadeOnDelete();
            $table->unsignedInteger('limit');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('account_service_plan_product_service_type');
    }
};
