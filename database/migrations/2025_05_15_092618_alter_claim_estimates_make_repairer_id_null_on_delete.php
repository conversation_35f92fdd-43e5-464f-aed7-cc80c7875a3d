<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('claim_estimates', function (Blueprint $table) {
            $table->dropForeign(['repairer_id']);
            $table->foreign('repairer_id')->references('id')->on('repairers')->nullOnDelete();
        });
    }
};
