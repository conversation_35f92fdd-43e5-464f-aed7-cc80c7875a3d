<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->string('payable_type')->nullable()->after('billing_request_id');
            $table->unsignedBigInteger('payable_id')->nullable()->after('payable_type');
        });

        \App\Models\Payment::query()
            ->with(['invoice', 'billingRequest.sale'])
            ->each(function (\App\Models\Payment $payment) {
                \App\Models\Payment::withoutTimestamps(function () use ($payment) {
                    $payable = $payment->invoice ?: $payment->billingRequest->sale;
                    if (! $payable) {
                        echo "Payment {$payment->id} has no payable\n";

                        return;
                    }
                    $payment->payable()->associate($payable);
                    $payment->save();
                });
            });

        Schema::table('payments', function (Blueprint $table) {
            $table->dropConstrainedForeignId('invoice_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->dropColumn('payable_type');
            $table->dropColumn('payable_id');
        });
    }
};
