<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (app()->runningUnitTests()) {
            return;
        }

        Schema::table('account_warranty_products', function (Blueprint $table) {
            $table->dropForeign('account_product_product_id_foreign');
            $table->dropIndex('account_product_product_id_foreign');
            $table->dropForeign('account_product_account_id_foreign');

            $table->dropUnique('unique_aggregate_unique');
            $table->unique([
                'account_id',
                'product_id',
                'max_engine_capacity',
                'max_mileage',
                'max_age',
                'annual_mileage_limit',
            ], 'unique_aggregate_unique');

            $table->foreign('account_id')->references('id')->on('accounts');
            $table->foreign('product_id')->references('id')->on('warranty_products');
        });
    }
};
