<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sales_offers', function (Blueprint $table) {
            $table->id();
            $table->uuid();
            $table->foreignIdFor(\App\Models\SalesLead::class)->constrained();
            $table->foreignIdFor(\App\Models\WarrantyProduct::class)->nullable()->constrained();
            $table->foreignIdFor(\App\Models\BreakdownProduct::class)->nullable()->constrained();
            $table->foreignIdFor(\App\Models\ServicePlanProduct::class)->nullable()->constrained();
            $table->decimal('warranty_selling_price', 7, 2)->nullable();
            $table->decimal('breakdown_selling_price', 7, 2)->nullable();
            $table->decimal('service_plan_selling_price', 7, 2)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sales_offers');
    }
};
