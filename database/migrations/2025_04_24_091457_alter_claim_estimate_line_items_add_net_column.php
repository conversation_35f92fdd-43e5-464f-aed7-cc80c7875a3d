<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('claim_estimate_line_items', function (Blueprint $table) {
            $table->decimal('net', 7, 2)
                ->storedAs('(quantity * amount) - COALESCE(customer_contribution, 0)')
                ->after('customer_contribution')
                ->comment('The net cost of the line item, excluding VAT');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('claim_estimate_line_items', function (Blueprint $table) {
            $table->dropColumn('net');
        });
    }
};
