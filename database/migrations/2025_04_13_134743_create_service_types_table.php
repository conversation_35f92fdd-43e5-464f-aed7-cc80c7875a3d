<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('service_types', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->unsignedInteger('position');
            $table->timestamps();
        });

        if (! app()->runningUnitTests()) {
            \Illuminate\Support\Facades\Artisan::call('db:seed', [
                '--class' => \Database\Seeders\ServiceTypeSeeder::class,
                '--force' => true,
            ]);
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('service_types');
    }
};
