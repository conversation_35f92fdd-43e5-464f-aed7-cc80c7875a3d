<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('phone_call_transcripts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('phone_call_id')->unique()->constrained()->cascadeOnDelete();
            $table->text('text');
            $table->unsignedInteger('processing_ms');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('phone_call_transcripts');
    }
};
