<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('repairers', function (Blueprint $table) {
            $table->string('vat_number')->nullable()->after('postcode');
            $table->string('company_number')->nullable()->after('vat_number');
            $table->string('bank_sort_code')->nullable()->after('company_number');
            $table->string('bank_account_number')->nullable()->after('bank_sort_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('repairers', function (Blueprint $table) {
            $table->dropColumn('vat_number');
            $table->dropColumn('company_number');
            $table->dropColumn('bank_sort_code');
            $table->dropColumn('bank_account_number');
        });
    }
};
