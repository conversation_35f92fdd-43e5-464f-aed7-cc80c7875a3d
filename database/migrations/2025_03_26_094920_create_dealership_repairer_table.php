<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dealership_repairer', function (Blueprint $table) {
            $table->id();
            $table->foreignId('dealership_id')->constrained()->cascadeOnDelete();
            $table->foreignId('repairer_id')->constrained()->cascadeOnDelete();
            $table->timestamps();

            $table->unique(['dealership_id', 'repairer_id']);
        });

        foreach (\App\Models\Repairer::all() as $repairer) {
            foreach (\App\Models\Account::find($repairer->account_id)->dealerships as $dealership) {
                $repairer->dealerships()->attach($dealership->id);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dealership_repairer');
    }
};
