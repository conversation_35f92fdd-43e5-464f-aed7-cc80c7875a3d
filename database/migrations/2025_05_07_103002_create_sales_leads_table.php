<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sales_leads', function (Blueprint $table) {
            $table->id();
            $table->foreignId('upselling_sale_id')->unique()->constrained('sales');
            $table->foreignId('assigned_to_user_id')->nullable()->constrained('users');
            $table->foreignId('resulting_sale_id')->nullable()->constrained('sales');
            $table->string('generator_class');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sales_leads');
    }
};
