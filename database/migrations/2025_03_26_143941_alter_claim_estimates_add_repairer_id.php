<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('claim_estimates', function (Blueprint $table) {
            $table->foreignId('repairer_id')->nullable()->after('claim_id')->constrained()->cascadeOnDelete();
            $table->unsignedInteger('current_mileage')->nullable()->after('work_required');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('claim_estimates', function (Blueprint $table) {
            $table->dropConstrainedForeignId('repairer_id');
            $table->dropColumn('current_mileage');
        });
    }
};
