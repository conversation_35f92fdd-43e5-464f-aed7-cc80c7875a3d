<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pay_later_plans', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(\App\Models\Account::class)->constrained()->cascadeOnDelete();
            $table->unsignedInteger('provider_plan_id');
            $table->string('name');
            $table->unsignedInteger('instalments');
            $table->boolean('deposit');
            $table->decimal('apr', 5, 3);
            $table->string('frequency');
            $table->decimal('commission_rate', 4, 2)->nullable();
            $table->decimal('commission_rate_margin', 4, 2)->nullable();
            $table->unsignedInteger('min_amount')->nullable();
            $table->unsignedInteger('max_amount');
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->unique(['account_id', 'provider_plan_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pay_later_plans');
    }
};
