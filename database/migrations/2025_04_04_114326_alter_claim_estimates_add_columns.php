<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('claim_estimates', function (Blueprint $table) {
            $table->string('reference')->nullable()->after('workshop_address');
            $table->string('drop_off_time')->nullable()->after('estimate_booking_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('claim_estimates', function (Blueprint $table) {
            $table->dropColumn('reference');
            $table->dropColumn('drop_off_time');
        });
    }
};
