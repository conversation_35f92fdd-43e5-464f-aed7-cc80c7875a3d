<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('billing_requests', function (Blueprint $table) {
            $table->string('provider')->nullable()->after('sale_id');
            $table->string('direct_debit_reference')->nullable()->after('mandate_activated_at');
            $table->dropColumn('payment_confirmed_at');
        });

        DB::table('billing_requests')->update(['provider' => 'go_cardless']);

        Schema::table('billing_requests', function (Blueprint $table) {
            $table->string('provider')->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('billing_requests', function (Blueprint $table) {
            $table->dropColumn('provider');
            $table->dropColumn('direct_debit_reference');
            $table->timestamp('payment_confirmed_at')->nullable()->after('mandate_activated_at');
        });
    }
};
