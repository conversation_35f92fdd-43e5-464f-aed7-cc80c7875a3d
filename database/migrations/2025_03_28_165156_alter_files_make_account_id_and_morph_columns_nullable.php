<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('files', function (Blueprint $table) {
            $table->foreignId('account_id')->nullable()->change();
            $table->string('related_type')->nullable()->change();
            $table->foreignId('related_id')->nullable()->change();
        });
    }
};
