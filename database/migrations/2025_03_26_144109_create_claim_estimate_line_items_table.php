<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('claim_estimate_line_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('claim_estimate_id')->constrained()->cascadeOnDelete();
            $table->foreignId('vehicle_component_id')->nullable()->constrained()->cascadeOnDelete();
            $table->string('type');
            $table->string('description')->nullable();
            $table->decimal('quantity', 5, 2)->unsigned()->default(1);
            $table->decimal('amount', 7, 2)->unsigned();
            $table->decimal('vat', 7, 2)->unsigned();
            $table->decimal('customer_contribution', 7, 2)->nullable()->unsigned();
            $table->decimal('total', 7, 2)->unsigned()->storedAs('(quantity * (amount + vat)) - COALESCE(customer_contribution, 0)');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('claim_estimate_line_items');
    }
};
