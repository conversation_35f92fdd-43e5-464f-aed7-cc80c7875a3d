<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('phone_calls', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('customer_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('dealership_id')->nullable()->constrained()->nullOnDelete();

            $table->unsignedInteger('call_log_segment_id')->unique();
            $table->unsignedInteger('call_log_call_id');
            $table->timestamp('started_at');

            $table->unsignedInteger('recording_id')->nullable();

            $table->unsignedSmallInteger('source_type')->nullable();
            $table->string('source_dn')->nullable();
            $table->string('source_caller_id')->nullable();
            $table->string('source_display_name')->nullable();
            $table->string('destination_type')->nullable();
            $table->string('destination_dn')->nullable();
            $table->string('destination_caller_id')->nullable();
            $table->string('destination_display_name')->nullable();
            $table->unsignedSmallInteger('action_type')->nullable();
            $table->string('action_dn_type')->nullable();
            $table->string('action_dn')->nullable();
            $table->string('action_dn_caller_id')->nullable();
            $table->string('action_dn_display_name')->nullable();
            $table->string('reason')->nullable();
            $table->string('source')->nullable();

            $table->decimal('ringing_duration', 8, 2)->nullable();
            $table->decimal('talking_duration', 8, 2)->nullable();
            $table->string('recording_url')->nullable();

            $table->boolean('answered')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('phone_calls');
    }
};
