<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('claim_estimates', function (Blueprint $table) {
            $table->renameColumn('vat_charged', 'is_charged_internally');
            $table->renameColumn('invoicing_dealer_direct', 'is_invoicing_dealer_direct');
            $table->string('workshop_name')->nullable()->change();
        });

        \Illuminate\Support\Facades\DB::table('claim_estimates')->update([
            'is_charged_internally' => DB::raw('1 - is_charged_internally'),
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('claim_estimates', function (Blueprint $table) {
            $table->renameColumn('is_charged_internally', 'vat_charged');
            $table->renameColumn('is_invoicing_dealer_direct', 'invoicing_dealer_direct');
        });

        \Illuminate\Support\Facades\DB::table('claim_estimates')->update([
            'vat_charged' => DB::raw('1 - vat_charged'),
        ]);
    }
};
