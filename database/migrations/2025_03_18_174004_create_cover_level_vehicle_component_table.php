<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cover_level_vehicle_component', function (Blueprint $table) {
            $table->id();
            $table->foreignId('cover_level_id')->constrained();
            $table->foreignId('vehicle_component_id')->constrained();
            $table->timestamps();

            $table->unique(['cover_level_id', 'vehicle_component_id'], 'covered_components_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cover_level_vehicle_component');
    }
};
