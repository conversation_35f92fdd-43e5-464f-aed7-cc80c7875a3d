<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('account_service_credentials', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(\App\Models\Account::class);
            $table->string('provider');
            $table->boolean('is_live')->default(false);
            $table->text('credentials');
            $table->timestamps();

            $table->unique(['account_id', 'provider', 'is_live']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('account_service_credentials');
    }
};
