<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vehicle_components', function (Blueprint $table) {
            $table->id();
            $table->foreignId('vehicle_component_category_id')->constrained();
            $table->string('name');
            $table->boolean('applicable_to_ice')->default(false);
            $table->boolean('applicable_to_ev')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vehicle_components');
    }
};
