<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('billing_requests', function (Blueprint $table) {
            $table->string('account_holder_name')->nullable()->after('direct_debit_reference');
            $table->string('sort_code', 6)->nullable()->after('account_holder_name');
            $table->string('account_number', 8)->nullable()->after('sort_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('billing_requests', function (Blueprint $table) {
            $table->dropColumn('account_holder_name');
            $table->dropColumn('sort_code');
            $table->dropColumn('account_number');
        });
    }
};
