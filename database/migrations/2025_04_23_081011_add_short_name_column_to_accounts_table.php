<?php

use App\Models\Account;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('accounts', function (Blueprint $table) {
            $table->string('short_name')->nullable()->after('name');
        });

        Account::query()->withTrashed()->each(function (Account $account) {
            $shortName = Str::shortName($account->name);

            if (strlen($shortName) > 10) {
                $shortName = substr($shortName, 0, 10);
            }

            $account->forceFill([
                'short_name' => $shortName,
            ])->save();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('accounts', function (Blueprint $table) {
            $table->dropColumn('short_name');
        });
    }
};
