<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pay_later_agreements', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(\App\Models\PayLaterPlan::class)->nullable()->constrained();
            $table->morphs('payable');
            $table->boolean('is_approved');
            $table->decimal('deposit_amount', 7, 2)->unsigned()->nullable();
            $table->decimal('loan_amount', 7, 2)->unsigned()->nullable();
            $table->decimal('commission_rate', 4, 2)->nullable();
            $table->decimal('commission_fixed_fee', 8, 2)->nullable();
            $table->decimal('commission_rate_margin', 8, 2)->nullable();
            $table->string('token')->nullable();
            $table->string('url')->nullable();
            $table->string('status')->nullable();
            $table->text('description')->nullable();
            $table->string('provider_reference')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pay_later_agreements');
    }
};
