<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cover_levels', function (Blueprint $table) {
            $table->boolean('is_ice')->default(false)->after('name');
            $table->boolean('is_ev')->default(false)->after('is_ice');
        });
        if (! app()->runningUnitTests()) {
            \Illuminate\Support\Facades\Artisan::call('db:seed', [
                '--class' => \Database\Seeders\CoverLevelSeeder::class,
                '--force' => true,
            ]);
        }
    }
};
