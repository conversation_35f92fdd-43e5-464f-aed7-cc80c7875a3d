<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('billing_requests', function (Blueprint $table) {
            $table->unique('provider_customer_id');
            $table->unique('provider_billing_request_id');
            $table->unique('mandate_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('billing_requests', function (Blueprint $table) {
            $table->dropUnique('billing_requests_provider_customer_id_unique');
            $table->dropUnique('billing_requests_provider_billing_request_id_unique');
            $table->dropUnique('billing_requests_mandate_id_unique');
        });
    }
};
