{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.4", "ext-exif": "*", "ext-gd": "*", "bugsnag/bugsnag-laravel": "^2.27", "chillerlan/php-qrcode": "^4.3", "cs278/bank-modulus": "^1.15", "doctrine/dbal": "^3.5", "filament/filament": "^3.2", "flowframe/laravel-trend": "^0.3.0", "gocardless/gocardless-pro": "^5.4", "guava/calendar": "^1.14", "guzzlehttp/guzzle": "^7.8", "laravel-notification-channels/twilio": "^4.0", "laravel/framework": "^11.41", "laravel/nightwatch": "^1.7", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "league/flysystem-aws-s3-v3": "^3.0", "livewire/flux": "^1.1", "livewire/flux-pro": "^1.1", "maatwebsite/excel": "^3.1", "mallardduck/blade-lucide-icons": "^1.23", "openai-php/laravel": "^0.10.2", "parallax/filament-comments": "^1.4", "prism-php/prism": "^0.55.0", "ratchet/pawl": "^0.4.3", "spatie/laravel-enum": "^3.1", "spatie/laravel-permission": "^6.9", "spatie/laravel-query-builder": "^6.2", "spatie/laravel-queueable-action": "^2.15", "stechstudio/filament-impersonate": "^3.15", "wnx/sidecar-browsershot": "^2.5", "zbateson/mail-mime-parser": "^3.0", "ovon-group/manufacturer-logos": "*"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.10", "fakerphp/faker": "^1.23", "larastan/larastan": "^3.0", "laravel/pail": "^1.2", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "pestphp/pest": "^3.7", "pestphp/pest-plugin-faker": "^3.0", "pestphp/pest-plugin-laravel": "^3.1", "pestphp/pest-plugin-livewire": "^3.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true, "repositories": {"flux-pro": {"type": "composer", "url": "https://composer.fluxui.dev"}, "manufacturer-logos": {"type": "path", "url": "packages/manufacturer-logos"}}}