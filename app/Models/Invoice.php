<?php

namespace App\Models;

use App\Filament\Resources\InvoiceResource;
use App\Models\Concerns\LinkableContract;
use App\Models\Traits\HasTenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Invoice extends Model implements LinkableContract
{
    use HasFactory;
    use HasTenant;

    const STATUS_DELETED = 'DELETED';

    const STATUS_PAID = 'PAID';

    const STATUS_VOIDED = 'VOIDED';

    const STATUS_PENDING = 'PENDING';

    const STATUS_AUTHORISED = 'AUTHORISED';

    const STATUS_SUBMITTED = 'SUBMITTED';

    protected static $unguarded = true;

    protected $casts = [
        'date' => 'date',
        'due_date' => 'date',
        'period_start' => 'date',
        'period_end' => 'date',
        'emailed_at' => 'datetime',
    ];

    public static function booted()
    {
        static::creating(function ($invoice) {
            $invoice->status = $invoice->status ?: 'PENDING';
        });
    }

    public function invoiceable(): MorphTo
    {
        return $this->morphTo('invoiceable')->withTrashed();
    }

    public function lineItems(): HasMany
    {
        return $this->hasMany(InvoiceLineItem::class);
    }

    public function payment(): MorphOne
    {
        return $this->morphOne(Payment::class, 'payable');
    }

    public function payments(): MorphMany
    {
        return $this->morphMany(Payment::class, 'payable');
    }

    public function claimAuthorisation(): HasOne
    {
        return $this->hasOne(ClaimAuthorisation::class);
    }

    public function scopeFilter($query, array $filters)
    {
        $query
            ->when($filters['start'] ?? null, fn ($query, $start) => $query->whereDate('created_at', '>=', $start))
            ->when($filters['end'] ?? null, fn ($query, $end) => $query->whereDate('created_at', '<=', $end))
            ->when($filters['search'] ?? null, fn ($query, $search) => $query
                ->where('invoice_number', 'like', '%'.$search.'%')
                ->orWhereHas('invoiceable', fn ($query) => $query
                    ->where('email', 'like', '%'.$search.'%')
                )
            );
    }

    public function markEmailSent()
    {
        $this->update(['emailed_at' => $this->freshTimestamp()]);
    }

    public function accountingSoftwareViewInvoiceUrl(): ?string
    {
        if ($this->accounting_software_id === null) {
            return null;
        }

        return sprintf(config('services.xero.view_invoice_url'), $this->accounting_software_id);
    }

    public function getTotal(): string
    {
        return $this->lineItems->sum('total');
    }

    public function getLabel(): string
    {
        return $this->invoice_number;
    }

    public function getAdminUrl(): string
    {
        return InvoiceResource::getUrl('view', ['record' => $this]);
    }
}
