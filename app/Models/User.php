<?php

namespace App\Models;

use App\Notifications\UserInvitation;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements FilamentUser
{
    use HasFactory;
    use HasRoles;
    use Notifiable;
    use SoftDeletes;

    protected $casts = [
        'email_verified_at' => 'datetime',
        'is_internal' => 'boolean',
    ];

    protected $appends = ['name'];

    public function resolveRouteBinding($value, $field = null)
    {
        return $this->where($field ?? 'id', $value)->withTrashed()->firstOrFail();
    }

    public function accounts(): BelongsToMany
    {
        return $this->belongsToMany(Account::class)->withTimestamps();
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class)->withTrashed();
    }

    public function sales(): HasMany
    {
        return $this->hasMany(Sale::class, 'sold_by_id');
    }

    public function voipUser(): HasOne
    {
        return $this->hasOne(VoipUser::class);
    }

    public function getNameAttribute()
    {
        return $this->first_name.' '.$this->last_name;
    }

    public function setPasswordAttribute($password)
    {
        $this->attributes['password'] = Hash::needsRehash($password) ? Hash::make($password) : $password;
    }

    public function salesPerson(): HasOne
    {
        return $this->hasOne(SalesPerson::class);
    }

    public function isDemoUser()
    {
        return $this->email === '<EMAIL>';
    }

    public function scopeOrderByName($query)
    {
        $query->orderBy('last_name')->orderBy('first_name');
    }

    public function scopeFilter($query, array $filters)
    {
        $query->when($filters['search'] ?? null, function ($query, $search) {
            $query->where(function ($query) use ($search) {
                $query->where('first_name', 'like', '%'.$search.'%')
                    ->orWhere('last_name', 'like', '%'.$search.'%')
                    ->orWhere('email', 'like', '%'.$search.'%');
            });
        })->when($filters['role'] ?? null, function ($query, $role) {
            $query->whereRole($role);
        })->when($filters['trashed'] ?? null, function ($query, $trashed) {
            if ($trashed === 'with') {
                $query->withTrashed();
            } elseif ($trashed === 'only') {
                $query->onlyTrashed();
            }
        });
    }

    public function scopeAdmin()
    {
        return $this->role('Admin');
    }

    public function scopeDealer()
    {
        return $this->role(['Dealership Manager', 'Dealership Sales']);
    }

    public function isAdmin(): bool
    {
        return $this->hasRole('Admin');
    }

    public function isSuperAdmin(): bool
    {
        return $this->roles->contains('name', 'Developer');
    }

    public function isOwner()
    {
        return $this->roles->contains('name', 'Owner');
    }

    public function isViewingAllRecords()
    {
        return $this->isAdmin() && $this->account_id === null;
    }

    public function canAccessPanel(Panel $panel): bool
    {
        return true;
    }

    public function isDealer()
    {
        return ! $this->isAdmin();
    }

    public function belongsToAccount(Account|int $account)
    {
        $accountId = is_int($account) ? $account : $account->id;

        return $this->accounts()->where('accounts.id', $accountId)->exists();
    }

    public function sendInviteNotification($token)
    {
        $this->notify(new UserInvitation($token));
    }

    public function canImpersonate(): bool
    {
        return $this->hasPermissionTo('users.impersonate');
    }

    public function isInternal()
    {
        return $this->is_internal;
    }
}
