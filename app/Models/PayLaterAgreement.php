<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class PayLaterAgreement extends Model
{
    /** @use HasFactory<\Database\Factories\PayLaterAgreementFactory> */
    use HasFactory;

    protected $casts = [
        'is_approved' => 'boolean',
    ];

    public function payable(): MorphTo
    {
        return $this->morphTo('payable');
    }

    public function payLaterPlan(): BelongsTo
    {
        return $this->belongsTo(PayLaterPlan::class);
    }

    public function scopeIncomplete($query)
    {
        return $query->where(fn ($q) => $q->whereNull('status')->orWhereIn('status', ['pending', 'failed']));
    }

    public function isActive(): bool
    {
        return $this->status === 'completed';
    }

    public function sendPaymentSetupNotification()
    {
        return $this->payable->sendPaymentSetupNotification();
    }
}
