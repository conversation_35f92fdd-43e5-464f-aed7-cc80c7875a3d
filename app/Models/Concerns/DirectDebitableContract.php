<?php

namespace App\Models\Concerns;

use App\Services\Payments\CustomerData;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

interface DirectDebitableContract
{
    public function requiresMandateSetup(): bool;

    public function hasActiveMandate(): bool;

    public function getMandateUrl(): ?string;

    public function getBillableEntityLabel(): string;

    public function getAccountHolderNameDefault(): string;

    public function toCustomerData(): CustomerData;

    public function metaDataForPaymentProvider(): array;

    public function notify($instance);

    public function sendMandateSetupNotification(): void;

    public function billingRequest(): BelongsTo;
}
