<?php

namespace App\Models;

use App\Enums\ProductStatus;
use App\Models\Traits\HasTenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Collection;

class ServicePlan extends Model
{
    /** @use HasFactory<\Database\Factories\ServicePlanFactory> */
    use HasFactory;

    use HasTenant;

    protected $casts = [
        'end_date' => 'date',
    ];

    public function product(): BelongsTo
    {
        return $this->belongsTo(ServicePlanProduct::class, 'service_plan_product_id');
    }

    public function sale(): BelongsTo
    {
        return $this->belongsTo(Sale::class)->withoutGlobalScope('confirmed');
    }

    public function serviceTypes(): BelongsToMany
    {
        return $this->belongsToMany(ServiceType::class)
            ->withPivot('limit')
            ->orderBy('service_types.position')
            ->withTimestamps();
    }

    public function authorisedServices()
    {
        return $this->hasMany(AuthorisedService::class);
    }

    public function getRemainingServiceTypes(): Collection
    {
        return $this->serviceTypes->filter(function (ServiceType $serviceType) {
            return $serviceType->pivot->limit > $this->authorisedServices->where('service_type_id', $serviceType->id)->count();
        })->values();
    }

    public function getStatusAttribute(): ProductStatus
    {
        if ($this->cancelled_at) {
            return ProductStatus::CANCELLED;
        }
        if ($this->end_date?->endOfDay()->isPast() && ! $this->isRecurring()) {
            return ProductStatus::EXPIRED;
        }
        if ($this->sale?->start_date->startOfDay()->isPast()) {
            return ProductStatus::LIVE;
        }

        return ProductStatus::PENDING;
    }

    public function isRecurring(): bool
    {
        return $this->product->is_recurring;
    }

    public function nominalAccountCode(): string
    {
        return config('accounting.nominal_codes.service_plan.held_for_dealer.code');
    }

    public function nominalAccountCodeForAdminFee(): string
    {
        return config('accounting.nominal_codes.service_plan.admin_fees.code');
    }

    public function scopePending($query)
    {
        return $query->notExpired()->notCancelled()->whereDate('start_date', '>', today());
    }

    public function scopeActive($query)
    {
        return $query->notExpired()->notCancelled()->whereDate('start_date', '<=', today());
    }

    public function scopeExpired($query, $expired = true)
    {
        return $query
            ->when($expired === true, fn ($q) => $q->whereNotNull('end_date')->whereDate('end_date', '<', today()))
            ->when($expired === false, fn ($q) => $q->where(fn ($q) => $q->whereNull('end_date')->orWhereDate('end_date', '>=', today())));
    }

    public function scopeNotExpired($query)
    {
        return $this->scopeExpired($query, false);
    }

    public function scopeCancelled($query)
    {
        return $query->whereNotNull('cancelled_at');
    }

    public function scopeNotCancelled($query)
    {
        return $query->whereNull('cancelled_at');
    }

    public function scopeRecurring($query)
    {
        return $query->whereHas('product', fn ($q) => $q->where('is_recurring', true));
    }
}
