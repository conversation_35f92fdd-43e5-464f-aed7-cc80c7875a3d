<?php

namespace App\Models;

use App\Enums\PaymentProvider;
use App\Models\Concerns\BillableContract;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Prunable;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class BillingRequest extends Model
{
    use HasFactory, Prunable;

    protected static $unguarded = true;

    protected array $activeStatuses = ['active', 'pending_submission'];

    protected $casts = [
        'expires_at' => 'datetime',
        'mandate_activated_at' => 'datetime',
        'provider' => PaymentProvider::class,
    ];

    public function sale(): HasOne
    {
        return $this->hasOne(Sale::class);
    }

    public function dealership(): HasOne
    {
        return $this->hasOne(Dealership::class);
    }

    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    public function isActive(): bool
    {
        return in_array($this->status, $this->activeStatuses);
    }

    public function scopeActive($query)
    {
        return $query
            ->whereIn('status', $this->activeStatuses)
            ->whereNotNull('mandate_activated_at');
    }

    public function isCompleted(): bool
    {
        return $this->mandate_activated_at !== null;
    }

    public function isExpired(): bool
    {
        if ($this->isCompleted()) {
            return false;
        }

        return $this->expires_at?->isPast();
    }

    public function expiresSoon(): bool
    {
        if ($this->isActive()) {
            return false;
        }

        return ! $this->expires_at || $this->expires_at->subDays(2)->isPast();
    }

    public function goCardlessUrl(): ?string
    {
        // TODO surface this in admin panel and show access paysuite URL too
        if ($this->mandate_id) {
            return config('payments.go_cardless.manage_url').'/mandates/'.$this->mandate_id;
        }

        return null;
    }

    public function getDirectDebitable(): ?BillableContract
    {
        return $this->sale ?? $this->dealership;
    }

    public function getAccountHolderName(): ?string
    {
        if ($this->account_holder_name) {
            return $this->account_holder_name;
        }

        return $this->getDirectDebitable()?->getAccountHolderNameDefault();
    }

    public function formattedSortCode(): ?string
    {
        if (! $this->sort_code) {
            return null;
        }

        return implode('-', str_split($this->sort_code, 2));
    }

    public function maskedAccountNumber(): ?string
    {
        if (! $this->account_number) {
            return null;
        }

        return str_repeat('*', strlen($this->account_number) - 4).substr($this->account_number, -4);
    }

    /**
     * Get the prunable model query.
     */
    public function prunable(): Builder
    {
        return static::query()
            ->whereNull('mandate_activated_at')
            ->whereDate('expires_at', '<=', now()->subDays(30));
    }
}
