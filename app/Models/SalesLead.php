<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SalesLead extends Model
{
    /** @use HasFactory<\Database\Factories\SalesLeadFactory> */
    use HasFactory;

    public function sale()
    {
        return $this->belongsTo(Sale::class, 'upselling_sale_id');
    }

    public function callOutcomes()
    {
        return $this->hasMany(SalesLeadCallOutcome::class);
    }

    public function offers()
    {
        return $this->hasMany(SalesOffer::class);
    }
}
