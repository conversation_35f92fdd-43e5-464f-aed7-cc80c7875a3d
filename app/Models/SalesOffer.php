<?php

namespace App\Models;

use App\Models\Traits\HasUuids;
use App\Notifications\SalesOfferNotification;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SalesOffer extends Model
{
    /** @use HasFactory<\Database\Factories\SalesOfferFactory> */
    use HasFactory;

    use HasUuids;

    protected $casts = [
        'sent_at' => 'datetime',
    ];

    public function salesLead()
    {
        return $this->belongsTo(SalesLead::class);
    }

    public function warrantyProduct()
    {
        return $this->belongsTo(WarrantyProduct::class);
    }

    public function send()
    {
        $this->salesLead->sale->customer->notify(new SalesOfferNotification($this));
        $this->update(['sent_at' => $this->freshTimestamp()]);
    }
}
