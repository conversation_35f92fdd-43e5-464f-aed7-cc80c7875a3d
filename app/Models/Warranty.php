<?php

namespace App\Models;

use App\Enums\ProductStatus;
use App\Models\Traits\HasFiles;
use App\Models\Traits\HasTenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Warranty extends Model
{
    use HasFactory;
    use HasFiles;
    use HasTenant;

    protected $casts = [
        'is_self_funded' => 'boolean',
        'start_date' => 'date',
        'end_date' => 'date',
        'cancelled_at' => 'datetime',
    ];

    public function sale(): BelongsTo
    {
        return $this->belongsTo(Sale::class)->withoutGlobalScope('confirmed');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(WarrantyProduct::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'sold_by_id');
    }

    public function claims(): HasMany
    {
        return $this->hasMany(Claim::class);
    }

    public function invoiceLineItems(): HasMany
    {
        return $this->hasMany(InvoiceLineItem::class, 'sale_id', 'sale_id')
            ->whereHas('invoice', fn ($q) => $q->where('invoiceable_type', 'customer'));
    }

    public function getMileageCutoffAttribute(): ?int
    {
        if (! $this->product_id) {
            return null;
        }

        if ($this->product->is_recurring) {
            return round($this->sale->delivery_mileage + ($this->start_date->diffInDays() * $this->annual_mileage_limit / 365));
        }

        return $this->sale->delivery_mileage + ($this->product->period / 12 * $this->annual_mileage_limit);
    }

    public function scopeActive($query)
    {
        return $query->notExpired()->notCancelled()->pending(false);
    }

    public function scopeLive($query)
    {
        return $query->notExpired()->notCancelled()->pending(false);
    }

    public function scopePending($query, $pending = true)
    {
        return $query
            ->notExpired()
            ->notCancelled()
            ->whereHas('sale', fn ($q) => $q->whereDate('start_date', ($pending ? '>=' : '<='), today()));
    }

    public function scopeExpired($query, $expired = true)
    {
        return $query
            ->when($expired === true, fn ($q) => $q->notCancelled()->whereNotNull('end_date')->whereDate('end_date', '<', today()))
            ->when($expired === false, fn ($q) => $q->where(fn ($q) => $q->whereNull('end_date')->orWhereDate('end_date', '>=', today())));
    }

    public function scopeNotExpired($query)
    {
        return $this->scopeExpired($query, false);
    }

    public function scopeCancelled($query)
    {
        return $query->whereNotNull('cancelled_at');
    }

    public function scopeNotCancelled($query)
    {
        return $query->whereNull('cancelled_at');
    }

    public function scopeRecurring($query)
    {
        return $query->whereNotNull('monthly_selling_price');
    }

    public function scopeFilter($query, array $filters)
    {
        $query
            ->when($filters['search'] ?? null, fn ($query, $search) => $query
                ->whereHas('sale', fn ($q) => $q->search($filters['search']))
            )
            ->when($filters['start'] ?? null, fn ($query, $start) => $query->whereHas('sale', fn ($q) => $q->whereDate('confirmed_at', '>=', $start)))
            ->when($filters['end'] ?? null, fn ($query, $end) => $query->whereHas('sale', fn ($q) => $q->whereDate('confirmed_at', '<=', $end)))
            ->when($filters['trashed'] ?? null, fn ($query, $trashed) => match ($trashed) {
                'with' => $query->withTrashed(),
                'only' => $query->onlyTrashed(),
                default => $query,
            });
    }

    public function getStatusAttribute(): ProductStatus
    {
        if ($this->cancelled_at) {
            return ProductStatus::CANCELLED;
        }
        if ($this->end_date?->endOfDay()->isPast() && ! $this->isRecurring()) {
            return ProductStatus::EXPIRED;
        }
        if ($this->sale?->start_date->startOfDay()->isPast()) {
            return ProductStatus::LIVE;
        }

        return ProductStatus::PENDING;
    }

    public function isRecurring(): bool
    {
        return $this->monthly_selling_price > 0;
    }

    public function initialCustomerInstallmentAmount(): ?float
    {
        if ($this->selling_price > 0) {
            // The dealer has taken an initial payment for this sale,
            // so we don't collect an initial payment from the customer.
            return null;
        }

        return $this->monthly_selling_price;
    }

    public function nominalAccountCode(): string
    {
        return match ($this->is_self_funded) {
            true => config('accounting.nominal_codes.warranty.subscription_self_funded.code'),
            false => config('accounting.nominal_codes.warranty.subscription_managed_fund.code'),
        };
    }

    public function nominalAccountCodeForAdminFee(): string
    {
        return match ($this->is_self_funded) {
            true => config('accounting.nominal_codes.warranty.self_funded_admin_fees.code'),
            false => config('accounting.nominal_codes.warranty.managed_fund_sales.code'),
        };
    }

    public function cancel()
    {
        $this->cancelled_at = now();
        $this->save();
    }

    public function statusColor()
    {
        return $this->status->color();
    }

    public function getAccountId(): int
    {
        return $this->account_id;
    }
}
