<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PhoneCall extends Model
{
    /** @use HasFactory<\Database\Factories\PhoneCallFactory> */
    use HasFactory;

    public $timestamps = false;

    protected $casts = [
        'started_at' => 'datetime',
        'answered' => 'boolean',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
