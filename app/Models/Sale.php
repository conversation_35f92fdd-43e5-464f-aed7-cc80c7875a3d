<?php

namespace App\Models;

use App\Events\SaleAdded;
use App\Filament\Resources\SaleResource;
use App\Models\Concerns\DirectDebitableContract;
use App\Models\Concerns\LinkableContract;
use App\Models\Traits\HasFiles;
use App\Models\Traits\HasTenant;
use App\Notifications\CustomerDirectDebitMandateNotification;
use App\Notifications\CustomerWelcomeAndDocumentNotification;
use App\Services\Payments\CustomerData;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Prunable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Str;

class Sale extends Model implements DirectDebitableContract, LinkableContract
{
    use HasFactory;
    use HasFiles;
    use HasTenant;
    use Prunable;

    protected $casts = [
        'registration_date' => 'date',
        'last_service_date' => 'date',
        'start_date' => 'date',
        'end_date' => 'date',
        'cancelled_at' => 'datetime',
        'confirmed_at' => 'datetime',
        'mot_last_checked_at' => 'datetime',
    ];

    protected static function booted()
    {
        // Global scope prevents unconfirmed sales from being returned.
        // There is an override on the addProducts page to allow unconfirmed sales to be confirmed.
        // Sales that have not been confirmed will be eventually cleaned up by a scheduled job.
        if (! app()->runningInConsole()) {
            static::addGlobalScope('confirmed', function (\Illuminate\Database\Eloquent\Builder $builder) {
                $builder->confirmed();
            });
        }
    }

    public function dealership(): BelongsTo
    {
        return $this->belongsTo(Dealership::class)->withTrashed();
    }

    public function warrantyClaims()
    {
        return $this->hasManyThrough(Claim::class, Warranty::class);
    }

    public function breakdownClaims()
    {
        return $this->hasManyThrough(BreakdownClaim::class, BreakdownPlan::class);
    }

    public function authorisedServices()
    {
        return $this->hasManyThrough(AuthorisedService::class, ServicePlan::class);
    }

    public function salesPerson(): BelongsTo
    {
        return $this->belongsTo(SalesPerson::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'sold_by_id');
    }

    public function warranty(): HasOne
    {
        return $this->hasOne(Warranty::class);
    }

    public function breakdownPlan(): HasOne
    {
        return $this->hasOne(BreakdownPlan::class);
    }

    public function servicePlan(): HasOne
    {
        return $this->hasOne(ServicePlan::class);
    }

    public function products()
    {
        return $this->hasMany(SaleProduct::class);
    }

    public function invoiceLineItems(): HasMany
    {
        return $this->hasMany(InvoiceLineItem::class);
    }

    public function billingRequest(): BelongsTo
    {
        return $this->belongsTo(BillingRequest::class);
    }

    public function payments(): MorphMany
    {
        return $this->morphMany(Payment::class, 'payable');
    }

    public function ownerChanges()
    {
        return $this->hasMany(OwnerChange::class);
    }

    public function getVehicleDetailsAttribute()
    {
        return sprintf('%s %s', $this->vehicle_make, $this->vehicle_model);
    }

    public function scopeConfirmed($query)
    {
        return $query->whereNotNull('confirmed_at');
    }

    public function scopeUnconfirmed($query)
    {
        return $query->whereNull('confirmed_at');
    }

    public function isConfirmed(): bool
    {
        return $this->confirmed_at !== null;
    }

    public function isUnconfirmed(): bool
    {
        return ! $this->isConfirmed();
    }

    public function scopeActive($query)
    {
        return $query
            ->confirmed()
            ->whereDate('start_date', '<=', Carbon::today())
            ->where(fn ($q) => $q
                ->whereHas('warranty', fn ($q) => $q->active())
                ->orWhereHas('breakdownPlan', fn ($q) => $q->active())
                ->orWhereHas('servicePlan', fn ($q) => $q->active())
            );
    }

    public function scopeActiveRecurring($query)
    {
        return $query
            ->confirmed()
            ->whereHas('warranty', fn ($q) => $q
                ->notCancelled()
                ->recurring()
            )
            ->orWhereHas('servicePlan', fn ($q) => $q
                ->notCancelled()
                ->recurring()
            );
    }

    public function scopeNeedsDirectDebit($query)
    {
        return $query
            ->activeRecurring()
            ->whereDoesntHave('billingRequest', fn ($q) => $q->active());
    }

    public function scopeWithFailedSubscriptionPayments($query)
    {
        return $query->activeRecurring()
            ->whereHas('payments', fn ($q) => $q->where('status', Payment::STATUS_FAILED));
    }

    public function scopeSearch($query, string $search)
    {
        $query->when($search, fn ($query) => $query
            ->where(fn ($q) => $q
                ->orWhere('id', $search)
                ->orWhere('vrm', 'like', '%'.$search.'%')
                ->orWhere('private_plate', 'like', '%'.$search.'%')
                ->orWhere('vehicle_make', 'like', '%'.$search.'%')
                ->orWhere('vehicle_model', 'like', '%'.$search.'%')
                ->orWhereHas('customer', fn ($q) => $q
                    ->where('email', 'like', $search.'%')
                    ->orWhere('last_name', 'like', $search.'%')
                    ->orWhere('postcode', 'like', $search.'%')
                )
            )
        );
    }

    public function scopeFilter($query, array $filters)
    {
        $query
            ->when($filters['search'] ?? null, fn ($query, $search) => $query->search($filters['search']))
            ->when($filters['start'] ?? null, fn ($query, $start) => $query->whereDate('sales.created_at', '>=', $start))
            ->when($filters['end'] ?? null, fn ($query, $end) => $query->whereDate('sales.created_at', '<=', $end))
            ->when($filters['trashed'] ?? null, fn ($query, $trashed) => match ($trashed) {
                'with' => $query->withTrashed(),
                'only' => $query->onlyTrashed(),
                default => $query,
            });
    }

    public function customerNeedsToSetupBilling(): bool
    {
        if (! $this->isRecurring()) {
            return false;
        }

        return ! $this->billingRequest?->isActive();
    }

    public function initialCustomerInstallmentAmount(): ?float
    {
        return $this->warranty->initialCustomerInstallmentAmount();
    }

    public function ongoingMonthlySellingPrice()
    {
        return $this->warranty->monthly_selling_price;
    }

    public function motTests(): HasMany
    {
        return $this->hasMany(MotTest::class)->orderByDesc('completed_at');
    }

    public function isRecurring(): bool
    {
        return $this->warranty?->isRecurring() || $this->breakdownPlan?->isRecurring() || $this->servicePlan?->isRecurring();
    }

    public function isHybrid(): bool
    {
        return Str::contains(strtolower($this->fuel_type), 'hybrid');
    }

    public function isPureElectric(): bool
    {
        return ! $this->isHybrid() && Str::contains(strtolower($this->fuel_type), 'electric');
    }

    public function isIce(): bool
    {
        return ! $this->isHybrid() && ! $this->isPureElectric();
    }

    public function expiryDate(): ?Carbon
    {
        return max(
            $this->warranty?->end_date,
            $this->breakdownPlan?->end_date,
            $this->servicePlan?->end_date
        );
    }

    public function hasActiveMandate(): bool
    {
        return (bool) $this->billingRequest?->isActive();
    }

    public function getMandateUrl(): ?string
    {
        return $this->billingRequest?->mandate_url;
    }

    public function requiresMandateSetup(): bool
    {
        return $this->isRecurring() && ! $this->hasActiveMandate();
    }

    public function getBillableEntityLabel(): string
    {
        return 'customer';
    }

    public function getAccountHolderNameDefault(): string
    {
        return $this->customer->first_name.' '.$this->customer->last_name;
    }

    public function getAccountId(): int
    {
        return $this->account_id;
    }

    public function toCustomerData(): CustomerData
    {
        return new CustomerData(
            reference: $this->id,
            paymentProcessorId: null,
            firstName: $this->customer->first_name,
            lastName: $this->customer->last_name,
            email: $this->customer->email,
            phone: $this->customer->phone,
            line1: $this->customer->address_1,
            line2: $this->customer->address_2,
            city: $this->customer->city,
            county: $this->customer->county,
            postcode: $this->customer->postcode,
            accountHolderName: $this->getAccountHolderNameDefault(),
        );
    }

    public function metaDataForPaymentProvider(): array
    {
        return [
            'customer_id' => (string) $this->customer_id,
            'sale_id' => (string) $this->id,
        ];
    }

    public function confirm(): void
    {
        $this->forceFill(['confirmed_at' => $this->freshTimestamp()])->save();

        SaleAdded::dispatch($this);
    }

    public function formattedVrm(): string
    {
        // https://gist.github.com/danielrbradley/7567269
        // ?<Current>^[A-Z]{2}[0-9]{2}\s?[A-Z]{3}$)|(?<Prefix>^[A-Z][0-9]{1,3}\s?[A-Z]{3}$)|(?<Suffix>^[A-Z]{3}\s?[0-9]{1,3}[A-Z]$)
        // Add appropriate spacing to the VRM

        // Determine the format of the VRM
        if (! preg_match('/(?<Current>^([A-Z]{2}[0-9]{2})\s?([A-Z]{3})$)|(?<Prefix>^([A-Z][0-9]{1,3})\s?([A-Z]{3})$)|(?<Suffix>^([A-Z]{3})\s?([0-9]{1,3}[A-Z])$)/', str_replace(' ', '', $this->vrm), $matches)) {
            // no match on these formats, return the VRM as is
            return $this->vrm;
        }

        // Return the last two array elements concatenated with a space
        return implode(' ', array_slice(array_filter($matches), -2));
    }

    public function notify($instance): void
    {
        $this->customer->notify($instance);
    }

    public function sendMandateSetupNotification(): void
    {
        $this->notify(new CustomerDirectDebitMandateNotification($this));
    }

    public function sendWelcomeAndDocumentNotification(): void
    {
        if ($this->documentsCanBeGenerated()) {
            $this->customer->notify(new CustomerWelcomeAndDocumentNotification($this));
        }
    }

    public function documentsCanBeGenerated(): bool
    {
        return ! $this->customerNeedsToSetupBilling();
    }

    /**
     * Get the prunable model query.
     */
    public function prunable(): Builder
    {
        return static::query()
            ->unconfirmed()
            ->where('created_at', '<=', now()->subDay());
    }

    public function getLabel(): string
    {
        return 'Sale #'.$this->id;
    }

    public function getAdminUrl(): string
    {
        return SaleResource::getUrl('view', [$this]);
    }
}
