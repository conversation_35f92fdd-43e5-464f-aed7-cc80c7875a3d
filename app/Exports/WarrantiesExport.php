<?php

namespace App\Exports;

use App\Models\Claim;
use App\Models\Warranty;
use Carbon\Carbon;

class WarrantiesExport extends BaseExport
{
    protected array $filters;

    protected bool $showAccount = false;

    public function query()
    {
        return Warranty::query()
            ->with([
                'sale.customer',
                'sale.dealership',
                'account',
                'claims' => fn ($q) => $q
                    ->withWhereHas('estimates.authorisation')
                    ->with('estimates.lineItems'),
            ])
            ->latest('warranties.id')
            ->groupBy('warranties.id')
            ->filter($this->filters);
    }

    public function headings(): array
    {
        return collect([
            $this->showAccount ? ['Account'] : [],
            [
                '#',
                'Funding Type',
                'Date',

                'VRM',
                'Private Plate',
                'VIN',
                'Make',
                'Model',
                'Derivative',
                'Engine Capacity',
                'Colour',
                'Fuel Type',
                'Transmission Type',
                'Registration Date',
                'Age at Delivery Date (years)',
                'Delivery Mileage',
                'Last Service Mileage',
                'Last Service Date',

                'Vehicle Price',
                'Provision',
                'Selling Price',
                'Initial Admin Fee',
                'Initial Input VAT',
                'Initial Sales VAT',
                'Monthly Selling Price (Gross)',
                'Monthly Admin Fee',
                'Monthly Provision',
                'Individual Claim Limit',
                'Total Claim Limit',

                'Start Date',
                'End Date',
                'Cancelled Date',

                'Dealer',

                'Last Name',
                'First Name',
                'Customer Email',
                'Customer Phone',
                'Address 1',
                'Address 2',
                'City',
                'County',
                'Country',
                'Postcode',

                'Authorised Claims',
                'Total Authorised Claims NET',
            ],
        ])->filter()->flatten()->toArray();
    }

    public function map($row): array
    {
        return collect([
            $this->showAccount && $row->account ? [$row->account->name] : [],
            [
                $row->sale->getKey(),
                $row->is_self_funded ? 'Self Funded' : 'Managed Fund',
                $row->created_at->format('d/m/Y'),

                $row->sale->vrm,
                $row->sale->private_plate,
                $row->sale->vin,
                $row->sale->vehicle_make,
                $row->sale->vehicle_model,
                $row->sale->vehicle_derivative,
                $row->sale->engine_capacity,
                $row->sale->vehicle_colour,
                $row->sale->fuel_type,
                $row->sale->transmission_type,
                Carbon::parse($row->sale->registration_date)->format('d/m/Y'),
                round(Carbon::parse($row->sale->registration_date)->diffInDays($row->sale->start_date) / 365, 1),
                $row->sale->delivery_mileage,
                $row->sale->last_service_mileage,
                $row->sale->last_service_date ? Carbon::parse($row->sale->last_service_date)->format('d/m/Y') : null,

                $row->sale->vehicle_price_paid,
                $row->provision,
                $row->selling_price,
                $row->admin_fee,
                $row->vat,
                $row->sales_vat,
                $row->monthly_selling_price,
                $row->monthly_admin_fee,
                $row->monthly_provision,
                $row->individual_claim_limit,
                $row->total_claim_limit,

                $row->sale->start_date->format('d/m/Y'),
                $row->end_date?->format('d/m/Y'),
                $row->cancelled_at?->format('d/m/Y') ?: '-',

                $row->sale->dealership->name,

                $row->sale->customer->last_name,
                $row->sale->customer->first_name,
                $row->sale->customer->email,
                $row->sale->customer->phone,

                $row->sale->customer->address_1,
                $row->sale->customer->address_2,
                $row->sale->customer->city,
                $row->sale->customer->county,
                $row->sale->customer->country,
                $row->sale->customer->postcode,

                $row->claims->count(),
                number_format($row->claims->sum(fn (Claim $claim) => $claim->authorisedNet()), 2, null, ''),
            ],
        ])->filter()->flatten()->toArray();
    }
}
