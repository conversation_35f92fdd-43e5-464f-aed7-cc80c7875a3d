<?php

namespace App\Exports;

use App\Models\BreakdownPlan;
use Carbon\Carbon;

class BreakdownPlansExport extends BaseExport
{
    public function query()
    {
        return BreakdownPlan::query()
            ->with(['sale.dealership', 'sale.customer', 'account'])
            ->latest()
            ->filter($this->filters);
    }

    public function headings(): array
    {
        return collect([
            $this->showAccount ? ['Account'] : [],
            [
                '#',
                'Funding',
                'Date',

                'VRM',
                'Private Plate',
                'VIN',
                'Make',
                'Model',
                'Derivative',
                'Engine Capacity',
                'Colour',
                'Fuel Type',
                'Transmission Type',
                'Registration Date',
                'Delivery Mileage',
                'Last Service Mileage',
                'Last Service Date',

                'Vehicle Price',
                'Provision',
                'Price',
                'Admin Fee',
                'Input VAT',
                'Sales VAT',

                'Start Date',
                'End Date',
                'Cancelled Date',

                'Dealer',

                'Last Name',
                'First Name',
                'Customer Email',
                'Customer Phone',
                'Address 1',
                'Address 2',
                'City',
                'County',
                'Country',
                'Postcode',
            ],
        ])->flatten()->toArray();
    }

    public function map($row): array
    {
        return collect([
            $this->showAccount ? $row->account->name : [],
            [
                $row->sale->getKey(),
                $row->is_self_funded ? 'Self Funded' : 'Managed Fund',
                $row->sale->created_at->format('d/m/Y'),

                $row->sale->vrm,
                $row->sale->private_plate,
                $row->sale->vin,
                $row->sale->vehicle_make,
                $row->sale->vehicle_model,
                $row->sale->vehicle_derivative,
                $row->sale->engine_capacity,
                $row->sale->vehicle_colour,
                $row->sale->fuel_type,
                $row->sale->transmission_type,
                Carbon::parse($row->sale->registration_date)->format('d/m/Y'),
                $row->sale->delivery_mileage,
                $row->sale->last_service_mileage,
                $row->sale->last_service_date ? Carbon::parse($row->sale->last_service_date)->format('d/m/Y') : null,

                $row->sale->vehicle_price_paid,
                $row->provision,
                $row->selling_price,
                $row->admin_fee,
                $row->vat,
                $row->sales_vat,

                $row->sale->start_date->format('d/m/Y'),
                $row->end_date->format('d/m/Y'),
                $row->sale->cancelled_at?->format('d/m/Y') ?: '-',

                $row->sale->dealership->name,

                $row->sale->customer->last_name,
                $row->sale->customer->first_name,
                $row->sale->customer->email,
                $row->sale->customer->phone,

                $row->sale->customer->address_1,
                $row->sale->customer->address_2,
                $row->sale->customer->city,
                $row->sale->customer->county,
                $row->sale->customer->country,
                $row->sale->customer->postcode,
            ],
        ])->flatten()->toArray();
    }
}
