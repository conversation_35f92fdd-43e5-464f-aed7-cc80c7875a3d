<?php

namespace App\Providers\Filament;

use App\Filament\Pages\BurnRateDashboard;
use App\Filament\Pages\ClaimsDashboard;
use App\Filament\Pages\ComponentList;
use App\Filament\Pages\OperationsDashboard;
use App\Filament\Pages\SalesDashboard;
use App\Filament\Resources\AccountResource;
use App\Filament\Resources\AiTaskResource;
use App\Filament\Resources\AuthorisedServiceResource;
use App\Filament\Resources\BreakdownClaimResource;
use App\Filament\Resources\BreakdownPlanResource;
use App\Filament\Resources\ClaimResource;
use App\Filament\Resources\CustomerResource;
use App\Filament\Resources\DealershipResource;
use App\Filament\Resources\DocumentResource;
use App\Filament\Resources\InboundEmailResource;
use App\Filament\Resources\InvoiceResource;
use App\Filament\Resources\PaymentResource;
use App\Filament\Resources\PayoutResource;
use App\Filament\Resources\RepairerResource;
use App\Filament\Resources\SaleResource;
use App\Filament\Resources\SalesPersonResource;
use App\Filament\Resources\ServicePlanResource;
use App\Filament\Resources\UserResource;
use App\Filament\Resources\WarrantyResource;
use App\Filament\Resources\WarrantyResource\Pages\ListWarrantyBurnRateReport;
use App\Http\Middleware\EnsureDealerHasAccountSet;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Infolists\Infolist;
use Filament\Navigation\NavigationBuilder;
use Filament\Navigation\NavigationGroup;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Assets\Js;
use Filament\Support\Colors\Color;
use Filament\Support\Facades\FilamentAsset;
use Filament\Support\Facades\FilamentView;
use Filament\Tables\Table;
use Filament\View\PanelsRenderHook;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Blade;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class AppPanelProvider extends PanelProvider
{
    public function register(): void
    {
        parent::register();

        Table::$defaultCurrency = 'gbp';
        Table::$defaultDateDisplayFormat = 'j M Y';
        Table::$defaultDateTimeDisplayFormat = 'j M Y H:i';
        Infolist::$defaultCurrency = 'gbp';
        Infolist::$defaultDateDisplayFormat = 'j M Y';
        Infolist::$defaultDateTimeDisplayFormat = 'j M Y H:i';

        FilamentView::registerRenderHook(
            PanelsRenderHook::USER_MENU_BEFORE,
            function (): string {
                if (auth()->user()->can('accounts.manage')) {
                    return Blade::render('<livewire:tenant-switcher></livewire:tenant-switcher>');
                }

                return '';
            }
        );
    }

    public function boot(): void
    {
        FilamentAsset::registerScriptData([
            'idealPostcodes' => [
                'key' => config('services.ideal_postcodes.api_key'),
            ],
        ]);

        FilamentAsset::register([
            Js::make('ideal-postcodes-script-1', 'https://cdn.jsdelivr.net/npm/@ideal-postcodes/address-finder-bundled'),
        ]);
    }

    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('app')
            ->domain(config('app.dealer_portal_domain'))
            ->default()
            ->sidebarCollapsibleOnDesktop()
            ->viteTheme('resources/css/filament/app/theme.css')
            ->brandLogo(asset('assets/protegoautocare.svg'))
            ->darkModeBrandLogo(asset('assets/protegoautocare-dark.svg'))
            ->brandLogoHeight('3rem')
            ->colors([
                'primary' => Color::Red,
                'gray' => Color::Gray,
            ])
            ->login()
            ->passwordReset()
            ->databaseNotifications()
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
                EnsureDealerHasAccountSet::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            ->readOnlyRelationManagersOnResourceViewPagesByDefault(false)
            ->navigation(function (NavigationBuilder $builder) {
                return $builder
                    ->items([
                        ...static::getDashboardNavigationItems(OperationsDashboard::class),
                        ...static::getDashboardNavigationItems(CustomerResource::class),
                        ...static::getDashboardNavigationItems(SaleResource::class),
                        ...static::getDashboardNavigationItems(DealershipResource::class),
                        ...static::getDashboardNavigationItems(SalesPersonResource::class),
                        ...static::getDashboardNavigationItems(RepairerResource::class),
                        ...static::getDashboardNavigationItems(InvoiceResource::class),
                        ...static::getDashboardNavigationItems(PaymentResource::class),
                        ...static::getDashboardNavigationItems(PayoutResource::class),
                    ])
                    ->groups([
                        NavigationGroup::make('Dashboards')
                            ->icon('sales')
                            ->items([
                                ...static::getDashboardNavigationItems(SalesDashboard::class),
                                ...static::getDashboardNavigationItems(ClaimsDashboard::class),
                                ...static::getDashboardNavigationItems(BurnRateDashboard::class),
                            ]),
                        NavigationGroup::make('Warranties')
                            ->icon('claims')
                            ->items([
                                ...static::getDashboardNavigationItems(WarrantyResource::class),
                                ...static::getDashboardNavigationItems(ClaimResource::class),
                            ]),
                        NavigationGroup::make('Breakdown Plans')
                            ->icon('claims')
                            ->items([
                                ...static::getDashboardNavigationItems(BreakdownPlanResource::class),
                                ...static::getDashboardNavigationItems(BreakdownClaimResource::class),
                            ]),
                        NavigationGroup::make('Service Plans')
                            ->icon('repairers')
                            ->items([
                                ...static::getDashboardNavigationItems(ServicePlanResource::class),
                                ...static::getDashboardNavigationItems(AuthorisedServiceResource::class),
                            ]),
                        //                        NavigationGroup::make('Claims')
                        //                            ->icon('claims')
                        //                            ->items([
                        //                                ...static::getDashboardNavigationItems(ClaimResource::class),
                        //                                ...static::getDashboardNavigationItems(BreakdownClaimResource::class),
                        //                            ]),
                        NavigationGroup::make('Reports')
                            ->icon('heroicon-o-chart-bar')
                            ->items([
                                ...static::getDashboardNavigationItems(ListWarrantyBurnRateReport::class),
                            ]),
                        NavigationGroup::make('Settings')
                            ->icon('heroicon-o-adjustments-vertical')
                            ->items([
                                ...static::getDashboardNavigationItems(UserResource::class),
                                ...static::getDashboardNavigationItems(AccountResource::class),
                                ...static::getDashboardNavigationItems(DocumentResource::class),
                                ...static::getDashboardNavigationItems(ComponentList::class),
                            ]),
                        NavigationGroup::make('AI')
                            ->icon('heroicon-o-adjustments-vertical')
                            ->items([
                                ...static::getDashboardNavigationItems(AiTaskResource::class),
                                ...static::getDashboardNavigationItems(InboundEmailResource::class),
                            ]),
                    ]);
            })
            ->bootUsing(function () {
                Table::configureUsing(fn ($table) => $table->paginationPageOptions([10, 25, 50]));
            });
    }

    private static function getDashboardNavigationItems(string $class)
    {
        return $class::canAccess() ? $class::getNavigationItems() : [];
    }
}
