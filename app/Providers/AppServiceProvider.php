<?php

namespace App\Providers;

use App\Models\Account;
use App\Models\BreakdownClaim;
use App\Models\Claim;
use App\Models\ClaimAuthorisation;
use App\Models\ClaimEstimate;
use App\Models\Customer;
use App\Models\Dealership;
use App\Models\File;
use App\Models\InboundEmail;
use App\Models\Invoice;
use App\Models\Sale;
use App\Models\User;
use App\Models\Warranty;
use App\Services\Accounting\AccountingService;
use App\Services\Accounting\XeroAccountingService;
use App\Services\MotHistoryService\MotHistoryService;
use App\Services\Payments\AccessPaysuitePaymentProcessor;
use App\Services\Payments\GoCardlessPaymentProcessor;
use App\Services\Payments\MultiProviderPaymentProcessor;
use App\Services\Payments\PaymentProcessor;
use App\Services\PlacesAutocomplete\GooglePlacesAutocomplete;
use App\Services\VehicleLookupService\CachedVehicleLookupService;
use App\Services\VehicleLookupService\MotorSpecVehicleLookupService;
use App\Services\VehicleLookupService\VehicleLookupService;
use App\Services\Voip\Voip3cxProvider;
use App\Services\WorkingDays\UkBankHolidayService;
use App\Services\WorkingDays\WorkingDaysService;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;
use Illuminate\Support\Stringable;

class AppServiceProvider extends ServiceProvider
{
    /**
     * The path to the "home" route for your application.
     *
     * This is used by Laravel authentication to redirect users after login.
     *
     * @var string
     */
    public const HOME = '/';

    /**
     * Register any application services.
     */
    public function register(): void
    {
        Model::unguard();
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        /** @var \Illuminate\Foundation\Application $app */
        $app = $this->app;
        Model::preventLazyLoading($app->isLocal());

        Carbon::macro('formatLocal', function () {
            return $this->tz('Europe/London')->format('j M Y');
        });

        Carbon::macro('formatLocalDateTime', function () {
            return $this->tz('Europe/London')->format('j M Y H:i');
        });

        Str::macro('shortName', function (string $string) {
            return Str::of($string)->headline()->title()->explode(' ')->first();
        });

        Stringable::macro('shortName', function () {
            return new Stringable(Str::shortName($this->value));
        });

        Gate::before(function (User $user, $ability, $params = []) {
            if ($user->isSuperAdmin() && in_array($ability, ['viewAny', 'view']) && ($params[0] ?? null) !== Account::class) {
                return true;
            }

            return null;
        });

        $this->app->singleton(GooglePlacesAutocomplete::class, function () {
            return new GooglePlacesAutocomplete(
                $this->app['config']['services.google_places']
            );
        });

        $this->app->bind(VehicleLookupService::class, function () {
            //                        return new DvlaVehicleLookupService($this->app['config']['services.dvla_ves']);
            return new CachedVehicleLookupService(
                $this->app['config']['services.motorspecs'],
                $this->app['cache'],
                new MotorSpecVehicleLookupService($this->app['config']['services.motorspecs'])
            );
        });

        $this->app->singleton(AccountingService::class, XeroAccountingService::class);
        $this->app->bind(XeroAccountingService::class, function () {
            return new XeroAccountingService(
                $this->app['cache'],
                $this->app['config']['services.xero'],
            );
        });

        $this->app->bind(GoCardlessPaymentProcessor::class, function () {
            return new GoCardlessPaymentProcessor(
                $this->app['config']['payments.go_cardless'],
            );
        });

        $this->app->bind(AccessPaysuitePaymentProcessor::class, function () {
            return new AccessPaysuitePaymentProcessor(
                $this->app['config']['payments.access_paysuite'],
            );
        });

        $this->app->bind(MultiProviderPaymentProcessor::class, function () {
            return new MultiProviderPaymentProcessor(
                $this->app['config']['payments'],
                [
                    $this->app[AccessPaysuitePaymentProcessor::class],
                    $this->app[GoCardlessPaymentProcessor::class],
                ]
            );
        });

        $this->app->bind(PaymentProcessor::class, MultiProviderPaymentProcessor::class);

        $this->app->bind(MotHistoryService::class, function () {
            return new MotHistoryService(
                $this->app['config']['services.dvsa_mot_history'],
                $this->app['cache'],
            );
        });

        $this->app->bind(Voip3cxProvider::class, function () {
            return new Voip3cxProvider(
                $this->app['cache'],
                $this->app['config']['services.3cx'],
            );
        });

        $this->app->bind(UkBankHolidayService::class, function () {
            return new UkBankHolidayService(
                $this->app['cache'],
                $this->app['config']['services.uk_bank_holidays'],
            );
        });

        $this->app->bind(WorkingDaysService::class, function () {
            return new WorkingDaysService(
                $this->app[UkBankHolidayService::class],
            );
        });

        Relation::enforceMorphMap([
            'invoice' => Invoice::class,
            'sale' => Sale::class,
            'warranty' => Warranty::class,
            'claim' => Claim::class,
            'estimate' => ClaimEstimate::class,
            'authorisation' => ClaimAuthorisation::class,
            'breakdown-claim' => BreakdownClaim::class,
            'dealership' => Dealership::class,
            'customer' => Customer::class,
            'user' => User::class,
            'file' => File::class,
            'inbound-email' => InboundEmail::class,
        ]);

        $this->bootAuth();
        $this->bootRoute();
    }

    public function bootAuth(): void
    {
        ResetPassword::createUrlUsing(function (User $user, string $token) {
            return url(route('password.reset', [
                'broker' => 'reset',
                'token' => $token,
                'email' => $user->getEmailForPasswordReset(),
            ], false));
        });
    }

    public function bootRoute(): void
    {
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });
    }
}
