<?php

namespace App\DataTransferObjects;

class ClaimValidationResult
{
    public function __construct(
        public readonly bool $validMileage,
        public readonly bool $validMot,
        public readonly bool $validOwnership,
        public readonly bool $needsServiceProof,
        public readonly bool $customerAcceptedTerms,
        public readonly ?string $lastOwnerChangeDate,
        public readonly ?string $motExpiryDate,
    ) {}
}
