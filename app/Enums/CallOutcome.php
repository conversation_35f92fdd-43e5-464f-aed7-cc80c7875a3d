<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum CallOutcome: string implements Has<PERSON>abe<PERSON>
{
    case INTERESTED = 'Interested';
    case NOT_INTERESTED = 'Not interested';
    case CALLBACK = 'Callback';
    case NO_ANSWER = 'No answer';
    case ENGAGED = 'Engaged';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::INTERESTED => 'Interested',
            self::NOT_INTERESTED => 'Not interested',
            self::CALLBACK => 'Callback',
            self::NO_ANSWER => 'No answer',
            self::ENGAGED => 'Engaged',
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::INTERESTED => 'success',
            self::NOT_INTERESTED, self::NO_ANSWER => 'danger',
            self::CALLBACK, self::ENGAGED => 'warning',
        };
    }

    public function calendarColor(): string
    {
        return match ($this->color()) {
            'success' => '#27b211',
            'danger' => '#ff5733',
            'warning' => '#ffbd33',
        };
    }
}
