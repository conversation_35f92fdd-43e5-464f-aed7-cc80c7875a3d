<?php

namespace App\Enums;

enum SupportChannel: string
{
    case PHONE = 'phone';
    case EMAIL = 'email';
    case LETTER = 'letter';

    public function label(): string
    {
        return match ($this) {
            self::PHONE => 'Phone',
            self::EMAIL => 'Email',
            self::LETTER => 'Letter',
        };
    }

    public static function toSelectArray(): array
    {
        return collect(self::cases())->mapWithKeys(fn (self $channel) => [$channel->value => $channel->label()])->toArray();
    }
}
