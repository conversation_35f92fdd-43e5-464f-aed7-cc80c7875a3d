<?php

namespace App\Enums;

enum ProductStatus: string
{
    case PENDING = 'pending';
    case LIVE = 'live';
    case EXPIRED = 'expired';
    case CANCELLED = 'cancelled';

    public function label(): string
    {
        return match ($this) {
            self::PENDING => 'Pending',
            self::LIVE => 'Live',
            self::EXPIRED => 'Expired',
            self::CANCELLED => 'Cancelled',
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::PENDING => 'info',
            self::LIVE => 'success',
            self::EXPIRED => 'warning',
            self::CANCELLED => 'danger',
        };
    }

    public static function toSelectArray(): array
    {
        return [
            self::PENDING->value => 'Pending',
            self::LIVE->value => 'Live',
            self::EXPIRED->value => 'Expired',
            self::CANCELLED->value => 'Cancelled',
        ];
    }
}
