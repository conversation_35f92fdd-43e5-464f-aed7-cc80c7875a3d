<?php

namespace App\Filament\Resources\CustomerResource\RelationManagers;

use App\Models\PhoneCall;
use App\Services\Voip\Voip3cxProvider;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class PhoneCallsRelationManager extends RelationManager
{
    protected static string $relationship = 'phoneCalls';

    public function table(Table $table): Table
    {
        return $table
            ->defaultSort('started_at', 'DESC')
            ->paginationPageOptions([20])
            ->columns([
                Tables\Columns\TextColumn::make('started_at')
                    ->sortable()
                    ->dateTime(),
                Tables\Columns\TextColumn::make('type')
                    ->getStateUsing(fn (PhoneCall $record) => $record->source_type === 0 ? 'Out' : 'In'),
                Tables\Columns\TextColumn::make('user.name'),
                Tables\Columns\TextColumn::make('ringing_duration')
                    ->sortable()
                    ->formatStateUsing(fn ($state) => gmdate('H:i:s', $state)),
                Tables\Columns\TextColumn::make('talking_duration')
                    ->sortable()
                    ->formatStateUsing(fn ($state) => gmdate('H:i:s', $state)),
                Tables\Columns\IconColumn::make('answered')
                    ->alignCenter()
                    ->boolean(),
                Tables\Columns\TextColumn::make('reason'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\Action::make('download_recording')
                    ->hiddenLabel()
                    ->icon('heroicon-o-arrow-down-tray')
                    ->visible(fn (PhoneCall $call) => $call->recording_id)
                    ->url(fn (PhoneCall $call, Voip3cxProvider $voip) => $voip->recordingUrl($call->recording_id)),
            ]);
    }
}
