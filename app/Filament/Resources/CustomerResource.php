<?php

namespace App\Filament\Resources;

use App\Actions\MakeVoipCall;
use App\Filament\Fields\AddressLookup;
use App\Filament\Resources\CustomerResource\Pages;
use App\Filament\Resources\CustomerResource\RelationManagers;
use App\Filament\Tables\Columns\CustomerColumn;
use App\Models\Customer;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists\Components\Actions\Action;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rules\Unique;

class CustomerResource extends Resource
{
    protected static ?string $model = Customer::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    public static function getRecordTitle(?Model $record): ?string
    {
        return $record?->full_name;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->columns(2)
                    ->inlineLabel()
                    ->schema([
                        Forms\Components\TextInput::make('first_name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('last_name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('email')
                            ->unique(ignoreRecord: true, modifyRuleUsing: fn (Unique $rule) => $rule->where('account_id', Auth::user()->account_id))
                            ->email()
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('phone')
                            ->tel()
                            ->required()
                            ->maxLength(255),
                    ]),
                Forms\Components\Section::make('Address')
                    ->maxWidth('2xl')
                    ->inlineLabel()
                    ->schema([
                        AddressLookup::make('address_1'),
                        Forms\Components\TextInput::make('address_2')
                            ->id('line_2')
                            ->label('Line 2')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('city')
                            ->id('post_town')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('county')
                            ->id('county')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('country')
                            ->id('country')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('postcode')
                            ->id('postcode')
                            ->required()
                            ->maxLength(255),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('Reference')
                    ->sortable(),
                Tables\Columns\TextColumn::make('account.short_name')
                    ->label('Account')
                    ->toggleable()
                    ->visible(fn () => Auth::user()->isViewingAllRecords())
                    ->sortable(),

                CustomerColumn::make('last_name')
                    ->sortable(),

                Tables\Columns\TextColumn::make('sales.vrm')
                    ->label('VRMs')
                    ->searchable()
                    ->wrap()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema(fn (Customer $record) => [
                static::getCustomerDetailsSection($record),
            ]);
    }

    public static function getCustomerDetailsSection(Customer $customer)
    {
        return Section::make('Customer Information')
            ->columns(3)
            ->schema([
                TextEntry::make('full_name')
                    ->icon('heroicon-o-user')
                    ->label('Customer')
                    ->url(static::getUrl('view', [$customer])),
                TextEntry::make('email')
                    ->icon('heroicon-o-envelope')
                    ->label('Email'),
                TextEntry::make('phone')
                    ->icon(auth()->user()->voipUser ? null : 'heroicon-o-phone')
                    ->label('Phone Number')
                    ->prefixAction(
                        Action::make('call_customer')
                            ->visible((bool) auth()->user()->voipUser)
                            ->icon('heroicon-s-phone')
                            ->hiddenLabel()
                            ->color('success')
                            ->button()
                            ->action(function (MakeVoipCall $makeVoipCall) use ($customer) {
                                $makeVoipCall->execute(auth()->user(), $customer);
                            })
                    ),
                TextEntry::make('address_1')
                    ->icon('heroicon-o-map-pin')
                    ->label('Address')
                    ->columnSpanFull()
                    ->getStateUsing($customer->fullAddress(', ')),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\SalesRelationManager::class,
            RelationManagers\PhoneCallsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCustomers::route('/'),
            'create' => Pages\CreateCustomer::route('/create'),
            'edit' => Pages\EditCustomer::route('/{record}/edit'),
            'view' => Pages\ViewCustomer::route('/{record}'),
        ];
    }
}
