<?php

namespace App\Filament\Resources\PaymentResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class PaymentLineItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'lineItems';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('description')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('description')
            ->paginated(false)
            ->columns([
                Tables\Columns\TextColumn::make('payable_type')
                    ->badge()
                    ->color('success')
                    ->formatStateUsing(function (Model $record) {
                        return strtoupper($record->payable_type);
                    })
                    ->label('Type'),
                Tables\Columns\TextColumn::make('description'),
                Tables\Columns\TextColumn::make('unit_amount')->money(),
                Tables\Columns\TextColumn::make('tax')->money(),
                Tables\Columns\TextColumn::make('total')->money(),
            ]);
    }
}
