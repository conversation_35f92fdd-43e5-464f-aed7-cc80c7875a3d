<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PaymentResource\Pages;
use App\Filament\Resources\PaymentResource\RelationManagers\PaymentLineItemsRelationManager;
use App\Models\Concerns\LinkableContract;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Sale;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Form;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;

class PaymentResource extends Resource
{
    protected static ?string $model = Payment::class;

    protected static ?string $navigationIcon = 'heroicon-o-currency-pound';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->withWhereHas('payable');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                DatePicker::make('charge_date')
                    ->maxWidth(MaxWidth::ExtraSmall)
                    ->minDate(today()->addDays(3))
                    ->native(false)
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->columns(self::getTableColumns(showSaleColumns: true))
            ->actions([
                Tables\Actions\EditAction::make()->slideOver(),
            ])
            ->filters([
                //
            ]);
    }

    public static function getTableColumns(bool $showSaleColumns = false): array
    {
        return [
            Tables\Columns\TextColumn::make('id')
                ->label('ID')
                ->sortable()
                ->searchable(),
            Tables\Columns\TextColumn::make('created_at')
                ->dateTime()
                ->sortable()
                ->toggleable(),
            Tables\Columns\TextColumn::make('charge_date')
                ->date()
                ->sortable(),
            Tables\Columns\IconColumn::make('processor_payment_id')->label('Sent')
                ->alignCenter()
                ->boolean(),
            Tables\Columns\TextColumn::make('payable')
                ->formatStateUsing(fn (?LinkableContract $state) => $state?->getLabel())
                ->placeholder('N/A')
                ->visible($showSaleColumns),
            Tables\Columns\TextColumn::make('period_start')
                ->date()
                ->sortable(),
            Tables\Columns\TextColumn::make('amount')
                ->numeric(2)
                ->prefix('£')
                ->sortable(),
            Tables\Columns\TextColumn::make('status')
                ->badge()
                ->color(fn (string $state): string => match ($state) {
                    Payment::STATUS_CANCELLED, Payment::STATUS_FAILED => 'danger',
                    Payment::STATUS_PENDING_SUBMISSION, Payment::STATUS_SUBMITTED => 'warning',
                    Payment::STATUS_PAID_OUT => 'success',
                    default => 'info',
                })
                ->searchable(),
            Tables\Columns\TextColumn::make('payout.reference')
                ->sortable(),
        ];
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make()
                    ->columns(4)
                    ->schema([
                        TextEntry::make('payable')
                            ->label(fn (Payment $payment) => ucwords($payment->payable_type))
                            ->formatStateUsing(fn (LinkableContract $state) => $state->getLabel())
                            ->url(fn (LinkableContract $state) => $state->getAdminUrl())
                            ->visible(fn (Payment $payment) => $payment->payable_id),
                        TextEntry::make('period_start')->date(),
                        TextEntry::make('charge_date')->date(),
                        TextEntry::make('payout.reference')
                            ->visible(fn (Payment $payment) => $payment->payout_id)
                            ->url(fn (Payment $payment) => $payment->payout_id ? "/payouts/{$payment->payout_id}" : null),
                        TextEntry::make('provider')
                            ->getStateUsing(fn (Payment $record) => $record->billingRequest->provider->name()),
                        TextEntry::make('status')
                            ->badge()
                            ->formatStateUsing(fn (Payment $payment): string => strtoupper(str_replace('_', ' ', $payment->status)))
                            ->color(fn (string $state): string => match ($state) {
                                Payment::STATUS_CANCELLED, Payment::STATUS_FAILED => 'danger',
                                Payment::STATUS_PENDING_SUBMISSION, Payment::STATUS_SUBMITTED => 'warning',
                                Payment::STATUS_PAID_OUT => 'success',
                                default => 'info',
                            }),
                        TextEntry::make('amount')->money(),
                        TextEntry::make('billed to')
                            ->getStateUsing(fn (Payment $payment) => match ($payment->payable_type) {
                                Relation::getMorphAlias(Sale::class) => $payment->payable->customer->full_name,
                                Relation::getMorphAlias(Invoice::class) => $payment->payable->invoiceable->name,
                                default => $payment->billingRequest->dealership->name ?? $payment->billingRequest->sale->customer->full_name ?? 'Unknown',
                            }),
                    ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            PaymentLineItemsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPayments::route('/'),
            'view' => Pages\ViewPayment::route('/{record}'),
        ];
    }
}
