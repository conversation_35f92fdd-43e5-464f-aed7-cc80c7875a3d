<?php

namespace App\Filament\Resources\InvoiceResource\Pages;

use App\Actions\GenerateInvoicePaymentForDealership;
use App\Actions\PullInvoiceFromAccountingSoftware;
use App\Actions\SyncInvoiceWithAccountingSoftware;
use App\Filament\Resources\InvoiceResource;
use App\Models\Invoice;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Auth;

class ViewInvoice extends ViewRecord
{
    protected static string $resource = InvoiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('generate_invoice_payment')
                ->visible(fn (Invoice $record) => Auth::user()->isAdmin() && ! $record->payment)
                ->label('Generate Payment')
                ->outlined()
                ->requiresConfirmation()
                ->action(function (Invoice $invoice, GenerateInvoicePaymentForDealership $generateInvoicePaymentForDealership, Action $action) {
                    try {
                        $generateInvoicePaymentForDealership->execute($invoice, sendToPaymentProvider: true);
                        $action->success();
                    } catch (\RuntimeException $e) {
                        Notification::make('generate_payment_error')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                })
                ->successNotificationTitle('Payment has been successfully created.'),
            Action::make('generate_invoice_in_xero')
                ->visible(fn (Invoice $record) => Auth::user()->isAdmin() && ! $record->accounting_software_id)
                ->label('Generate Invoice in Xero')
                ->requiresConfirmation()
                ->action(function (Invoice $invoice, SyncInvoiceWithAccountingSoftware $syncInvoiceWithAccountingSoftware, Action $action) {
                    $syncInvoiceWithAccountingSoftware->execute($invoice);
                    $action->success();
                })
                ->successNotificationTitle('Invoice has been successfully generated in Xero.'),
            Action::make('pull_invoice_from_xero')
                ->visible(Auth::user()->isSuperAdmin())
                ->label('Pull invoice from Xero')
                ->outlined()
                ->action(function (Invoice $invoice, PullInvoiceFromAccountingSoftware $pullInvoiceFromAccountingSoftware) {
                    $pullInvoiceFromAccountingSoftware->execute($invoice->accounting_software_id);
                    Notification::make('pull_xero_success')
                        ->body('Invoice has been successfully pulled from Xero.')
                        ->success()->send();
                }),
            Action::make('view_invoice_in_xero')
                ->visible(fn (Invoice $record) => Auth::user()->isAdmin() && $record->accounting_software_id)
                ->label('View in Xero')
                ->url(fn (Invoice $record) => $record->accountingSoftwareViewInvoiceUrl())
                ->openUrlInNewTab(),
        ];
    }
}
