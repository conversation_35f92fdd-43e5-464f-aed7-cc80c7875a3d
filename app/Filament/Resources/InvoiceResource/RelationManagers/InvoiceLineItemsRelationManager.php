<?php

namespace App\Filament\Resources\InvoiceResource\RelationManagers;

use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class InvoiceLineItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'lineItems';

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('description')
            ->paginated(false)
            ->columns([
                Tables\Columns\TextColumn::make('sale_id')
                    ->label('Sale')
                    ->url(fn (Model $record) => "/sales/{$record->sale_id}"),
                Tables\Columns\TextColumn::make('sale.vrm')
                    ->label('VRM'),
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color('success')
                    ->formatStateUsing(function (Model $record) {
                        return strtoupper($record->type);
                    })
                    ->label('Type'),
                Tables\Columns\TextColumn::make('quantity')->numeric(),
                Tables\Columns\TextColumn::make('description'),
                Tables\Columns\TextColumn::make('unit_amount')->money(),
                Tables\Columns\TextColumn::make('tax')->money()->label('VAT'),
                Tables\Columns\TextColumn::make('total')
                    ->money()
                    ->summarize(Tables\Columns\Summarizers\Sum::make()->money()->label('')),
            ]);
    }
}
