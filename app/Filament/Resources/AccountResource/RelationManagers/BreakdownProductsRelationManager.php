<?php

namespace App\Filament\Resources\AccountResource\RelationManagers;

use App\Models\AccountBreakdownProduct;
use App\Models\BreakdownProduct;
use Closure;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class BreakdownProductsRelationManager extends RelationManager
{
    protected static string $relationship = 'breakdownProducts';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('breakdown_product_id')
                    ->relationship('product', 'name', fn ($query) => $query
                        ->orderBy('position')
                    )
                    ->getOptionLabelFromRecordUsing(fn (BreakdownProduct $breakdownProduct) => $breakdownProduct->getLabel())
                    ->required(),
                Forms\Components\Section::make()
                    ->columns(3)
                    ->schema([
                        Forms\Components\TextInput::make('max_engine_capacity')
                            ->placeholder('Leave blank for no limit')
                            ->numeric(),
                        Forms\Components\TextInput::make('max_mileage')
                            ->placeholder('Leave blank for no limit')
                            ->numeric(),
                        Forms\Components\TextInput::make('max_age')
                            ->placeholder('Leave blank for no limit')
                            ->numeric(),
                    ]),
                Forms\Components\Section::make()
                    ->columns(3)
                    ->schema([
                        Forms\Components\TextInput::make('provision')
                            ->required()
                            ->numeric()
                            ->prefix('£'),
                        Forms\Components\TextInput::make('selling_price')
                            ->required()
                            ->numeric()
                            ->prefix('£'),
                        Forms\Components\TextInput::make('admin_fee')
                            ->visible($this->getOwnerRecord()->breakdown_self_funded)
                            ->required()
                            ->numeric()
                            ->prefix('£'),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->paginated(false)
            ->defaultGroup(
                Tables\Grouping\Group::make('product.position')
                    ->titlePrefixedWithLabel(false)
                    ->getDescriptionFromRecordUsing(fn (AccountBreakdownProduct $accountBreakdownProduct) => false ? 'Subscription' : 'Paid via dealer')
                    ->getTitleFromRecordUsing(fn (AccountBreakdownProduct $accountBreakdownProduct) => $accountBreakdownProduct->product->getLabel())
            )
            ->columns([
                Tables\Columns\TextColumn::make('max_engine_capacity')
                    ->numeric()
                    ->placeholder('-')
                    ->alignCenter()
                    ->sortable(),
                Tables\Columns\TextColumn::make('max_mileage')
                    ->numeric()
                    ->placeholder('-')
                    ->alignCenter()
                    ->sortable(),
                Tables\Columns\TextColumn::make('max_age')
                    ->numeric()
                    ->placeholder('-')
                    ->alignCenter()
                    ->sortable(),
                Tables\Columns\TextColumn::make('provision')
                    ->money()
                    ->placeholder('-')
                    ->alignCenter()
                    ->sortable(),
                Tables\Columns\TextColumn::make('selling_price')
                    ->money()
                    ->placeholder('-')
                    ->alignCenter()
                    ->sortable(),
                Tables\Columns\TextColumn::make('admin_fee')
                    ->visible(fn () => $this->getOwnerRecord()->breakdown_self_funded)
                    ->money()
                    ->placeholder('-')
                    ->alignCenter()
                    ->sortable(),
                //                Tables\Columns\TextColumn::make('monthly_selling_price')
                //                    ->money()
                //                    ->alignCenter()
                //                    ->sortable(),
                //                Tables\Columns\TextColumn::make('monthly_admin_fee')
                //                    ->money()
                //                    ->alignCenter()
                //                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing($this->mutateFormDataUsing())
                    ->label('Add Product')
                    ->slideOver(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->mutateFormDataUsing($this->mutateFormDataUsing())
                    ->slideOver(),
                Tables\Actions\DeleteAction::make(),
            ]);
    }

    protected function mutateFormDataUsing(): Closure
    {
        return function (array $data) {
            if (! $this->getOwnerRecord()->breakdown_self_funded) {
                $data['admin_fee'] = $data['provision'];
            }

            return $data;
        };
    }
}
