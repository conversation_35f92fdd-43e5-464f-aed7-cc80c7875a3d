<?php

namespace App\Filament\Resources\AccountResource\RelationManagers;

use App\Models\AccountServicePlanProduct;
use App\Models\ServicePlanProduct;
use App\Models\ServiceType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ServicePlanProductsRelationManager extends RelationManager
{
    protected static string $relationship = 'servicePlanProducts';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('service_plan_product_id')
                    ->relationship('product', 'name')
                    ->getOptionLabelFromRecordUsing(fn (ServicePlanProduct $product) => sprintf(
                        '%s%s',
                        $product->name,
                        $product->is_recurring ? ' - Monthly' : ''
                    ))
                    ->required(),
                Forms\Components\Select::make('duration_years')
                    ->maxWidth(MaxWidth::ExtraSmall)
                    ->options(function () {
                        return collect(range(1, 10))->mapWithKeys(fn ($value) => [$value => $value]);
                    })
                    ->required(),

                Forms\Components\Grid::make(3)
                    ->schema([
                        ...ServiceType::all()->map(fn ($type) => Forms\Components\TextInput::make("service_types.{$type->id}.limit")
                            ->label($type->name)
                            ->default('0')
                            ->required()
                            ->helperText('Total limit in plan lifetime')
                            ->minValue(0)
                            ->maxValue(10)
                            ->numeric()
                        ),
                    ]),

                Forms\Components\Section::make()
                    ->columns(3)
                    ->schema([
                        Forms\Components\TextInput::make('max_engine_capacity')
                            ->numeric(),
                        Forms\Components\TextInput::make('selling_price')
                            ->required()
                            ->numeric()
                            ->prefix('£'),
                        Forms\Components\TextInput::make('admin_fee')
                            ->required()
                            ->numeric()
                            ->prefix('£'),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->paginated(false)
            ->defaultSort(fn (Builder $query) => $query->orderBy(ServicePlanProduct::select('position')
                ->whereColumn('service_plan_product_id', 'service_plan_products.id')
            ))
            ->modifyQueryUsing(fn (Builder $query) => $query->with('serviceTypes'))
            ->columns([
                Tables\Columns\TextColumn::make('product.name')
                    ->sortable(),
                Tables\Columns\TextColumn::make('duration_years')->label('Duration (years)')
                    ->numeric()
                    ->placeholder('-')
                    ->alignCenter()
                    ->sortable(),
                Tables\Columns\TextColumn::make('max_engine_capacity')
                    ->numeric()
                    ->placeholder('-')
                    ->alignCenter()
                    ->sortable(),
                Tables\Columns\TextColumn::make('selling_price')
                    ->money()
                    ->placeholder('-')
                    ->alignCenter()
                    ->sortable(),
                Tables\Columns\TextColumn::make('admin_fee')
                    ->money()
                    ->placeholder('-')
                    ->alignCenter()
                    ->sortable(),
                ...ServiceType::all()->map(fn ($type) => Tables\Columns\TextColumn::make($type->name)
                    ->label($type->name)
                    ->wrap()
                    ->alignCenter()
                    ->placeholder('0')
                    ->getStateUsing(fn (AccountServicePlanProduct $record) => $record->serviceTypes->firstWhere('id', $type->id)?->pivot->limit)
                    ->numeric(),
                ),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->label('Add Product')
                    ->using(function ($livewire, array $data): AccountServicePlanProduct {
                        $serviceTypes = $data['service_types'];
                        unset($data['service_types']);

                        $accountServicePlanProduct = $livewire->getRelationship()->create($data);
                        $accountServicePlanProduct->serviceTypes()->sync($serviceTypes);

                        return $accountServicePlanProduct;

                    })
                    ->slideOver(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->fillForm(fn (AccountServicePlanProduct $record): array => [
                        ...$record->getAttributes(),
                        'service_types' => $record->serviceTypes->mapWithKeys(fn ($type) => [$type->id => ['limit' => $type->pivot->limit]]),
                    ])
                    ->using(function (AccountServicePlanProduct $record, array $data): AccountServicePlanProduct {
                        $serviceTypes = $data['service_types'];
                        unset($data['service_types']);

                        $record->update($data);
                        $record->serviceTypes()->sync($serviceTypes);

                        return $record;
                    })
                    ->slideOver(),
                Tables\Actions\DeleteAction::make(),
            ]);
    }
}
