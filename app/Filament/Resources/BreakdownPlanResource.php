<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BreakdownPlanResource\Pages;
use App\Filament\Resources\SaleResource\Pages\ViewBreakdownPlan;
use App\Filament\Tables\Columns\CustomerColumn;
use App\Filament\Tables\Columns\ProductStatusColumn;
use App\Filament\Tables\Columns\VehicleColumn;
use App\Models\BreakdownPlan;
use App\Models\ServicePlan;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class BreakdownPlanResource extends Resource
{
    protected static ?string $model = BreakdownPlan::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->with([
            'sale.customer',
            'sale.manufacturer',
        ]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('account_id')
                    ->relationship('account', 'name')
                    ->required(),
                Forms\Components\Select::make('sale_id')
                    ->relationship('sale', 'id')
                    ->required(),
                Forms\Components\TextInput::make('breakdown_product_id')
                    ->required()
                    ->numeric(),
                Forms\Components\Toggle::make('is_self_funded')
                    ->required(),
                Forms\Components\TextInput::make('provision')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('selling_price')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('admin_fee')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('vat')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('sales_vat')
                    ->required()
                    ->numeric(),
                Forms\Components\DatePicker::make('end_date')
                    ->required(),
                Forms\Components\DateTimePicker::make('cancelled_at'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('sale.id')
                    ->label('Sale #')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Date entered')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('sale.dealership.short_name')
                    ->label('Dealership')
                    ->toggleable()
                    ->visible(Auth::user()->isViewingAllRecords() || Auth::user()->account?->dealerships->count() > 1)
                    ->sortable(),
                Tables\Columns\TextColumn::make('product.name')
                    ->description(fn (ServicePlan $servicePlan) => sprintf('%s years', $servicePlan->duration_years))
                    ->label('Product')
                    ->sortable(),
                Tables\Columns\TextColumn::make('sale.start_date')
                    ->label('Start Date')
                    ->date()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('end_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('sale.salesPerson.name')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->sortable(),

                CustomerColumn::make('sale.customer'),

                VehicleColumn::make('vehicle'),

                ProductStatusColumn::make('status')
                    ->recurringBadge(fn (BreakdownPlan $breakdownPlan) => $breakdownPlan->isRecurring()),
                Tables\Columns\IconColumn::make('is_self_funded')
                    ->label('Self Funded')
                    ->alignCenter()
                    ->boolean(),
                Tables\Columns\TextColumn::make('provision')
                    ->money()
                    ->sortable(),
                Tables\Columns\TextColumn::make('selling_price')
                    ->money()
                    ->sortable(),
                Tables\Columns\TextColumn::make('admin_fee')
                    ->money()
                    ->sortable(),
                Tables\Columns\TextColumn::make('vat')
                    ->money()
                    ->sortable(),
                Tables\Columns\TextColumn::make('sales_vat')
                    ->money()
                    ->sortable(),
                Tables\Columns\TextColumn::make('end_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('cancelled_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make()->url(fn (BreakdownPlan $breakdownPlan) => ViewBreakdownPlan::getUrl([$breakdownPlan->sale_id])),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBreakdownPlans::route('/'),
            'create' => Pages\CreateBreakdownPlan::route('/create'),
            'edit' => Pages\EditBreakdownPlan::route('/{record}/edit'),
        ];
    }
}
