<?php

namespace App\Filament\Resources;

use App\Filament\Resources\InboundEmailResource\Pages;
use App\Filament\Resources\InboundEmailResource\RelationManagers\AttachmentsRelationManager;
use App\Models\InboundEmail;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use ZBateson\MailMimeParser\MailMimeParser;

class InboundEmailResource extends Resource
{
    protected static ?string $model = InboundEmail::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('timestamp')
                    ->label('Received at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('subject')
                    ->searchable(),
                Tables\Columns\TextColumn::make('source')
                    ->label('From')
                    ->searchable(),
                Tables\Columns\TextColumn::make('attachments_count')
                    ->label('Attachments')
                    ->counts('attachments')
                    ->searchable(),
                Tables\Columns\TextColumn::make('processed_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make()
                    ->columns(4)
                    ->schema([
                        TextEntry::make('timestamp')
                            ->label('Received at')
                            ->dateTime(),
                        TextEntry::make('subject'),
                        TextEntry::make('source')
                            ->label('From'),
                        TextEntry::make('content')
                            ->columnSpanFull()
                            ->placeholder('No content')
                            ->html()
                            ->getStateUsing(function (InboundEmail $inboundEmail, MailMimeParser $mailParser) {
                                $message = $mailParser->parse($inboundEmail->getContent(), false);

                                return strip_tags($message->getHtmlContent() ?: $message->getTextContent(), [
                                    'br',
                                    'p',
                                    'strong',
                                    'em',
                                    'u',
                                    's',
                                    'sub',
                                    'sup',
                                    'blockquote',
                                    'pre',
                                    'code',
                                    'h1',
                                    'h2',
                                    'h3',
                                    'h4',
                                    'h5',
                                    'h6',
                                    'ul',
                                    'ol',
                                    'li',
                                    'table',
                                    'tr',
                                    'td',
                                    'th',
                                ]);
                            }),
                    ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            AttachmentsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInboundEmails::route('/'),
            'create' => Pages\CreateInboundEmail::route('/create'),
            'view' => Pages\ViewInboundEmail::route('/{record}'),
        ];
    }
}
