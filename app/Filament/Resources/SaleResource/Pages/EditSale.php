<?php

namespace App\Filament\Resources\SaleResource\Pages;

use App\Filament\Resources\SaleResource;
use App\Models\Sale;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditSale extends EditRecord
{
    protected static string $resource = SaleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function afterSave()
    {
        /** @var Sale $sale */
        $sale = $this->getRecord();

        $sale->warranty?->update([
            'start_date' => $sale->start_date,
            'end_date' => $sale->start_date->copy()->addMonths($sale->warranty->product->period)->subDay(),
        ]);
        $sale->breakdownPlan?->update([
            'end_date' => $sale->start_date->copy()->addMonths($sale->breakdownPlan->product->period)->subDay(),
        ]);
        $sale->servicePlan?->update([
            'end_date' => $sale->start_date->copy()->addYears($sale->servicePlan->duration_years)->subDay(),
        ]);
    }
}
