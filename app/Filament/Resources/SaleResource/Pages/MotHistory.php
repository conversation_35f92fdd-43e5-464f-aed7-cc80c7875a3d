<?php

namespace App\Filament\Resources\SaleResource\Pages;

use App\Actions\FetchMotHistory;
use App\Filament\Resources\SaleResource;
use Filament\Resources\Pages\Concerns\InteractsWithRecord;
use Filament\Resources\Pages\Page;

class MotHistory extends Page
{
    use InteractsWithRecord;

    protected static string $resource = SaleResource::class;

    protected static string $view = 'filament.resources.sale-resource.pages.mot-history';

    protected static ?string $title = 'MOT History';

    public function mount(FetchMotHistory $fetchMotHistory)
    {
        $this->record = $this->resolveRecord($this->record);

        $this->record->load('motTests.defects');

        if (! $this->record->mot_last_checked_at || $this->record->mot_last_checked_at < now()->subDays(1)) {
            $fetchMotHistory->execute($this->record);
        }
    }

    public function getBreadcrumbs(): array
    {
        return [
            '/sales' => 'Sales',
            "/sales/{$this->record->id}" => 'View',
            'MOT History',
        ];
    }

    public function checkMotStatus(FetchMotHistory $fetchMotHistory)
    {
        $fetchMotHistory->execute($this->record);
    }
}
