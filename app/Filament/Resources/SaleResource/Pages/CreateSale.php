<?php

namespace App\Filament\Resources\SaleResource\Pages;

use App\Enums\VehicleType;
use App\Filament\Fields\VRMInput;
use App\Filament\Resources\CustomerResource;
use App\Filament\Resources\SaleResource;
use App\Models\Customer;
use App\Services\VehicleLookupService\VehicleLookupService;
use App\Services\VehicleLookupService\VehicleNotFoundException;
use Filament\Actions\Action;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Components\Wizard\Step;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Database\Eloquent\Builder;
use Livewire\Attributes\Url;

class CreateSale extends CreateRecord
{
    use CreateRecord\Concerns\HasWizard;

    protected static string $resource = SaleResource::class;

    protected static bool $canCreateAnother = false;

    #[Url(as: 'customer', except: '')]
    public $customerId = null;

    public function form(Form $form): Form
    {
        return $form->schema([
            Section::make('Customer')
                ->schema([
                    Select::make('customer_id')
                        ->required()
                        ->maxWidth(MaxWidth::ExtraLarge)
                        ->placeholder('Search customer email or name')
                        ->relationship('customer', 'email')
                        ->searchable(['email', 'last_name'])
                        ->getOptionLabelFromRecordUsing(fn (Customer $customer) => $customer->full_name.' - '.$customer->email)
                        ->live()
                        ->default($this->customerId)
                        ->createOptionForm(fn ($form) => CustomerResource::form($form))
                        ->createOptionAction(fn (\Filament\Forms\Components\Actions\Action $action) => $action
                            ->slideOver()
                            ->color('primary')
                        )
                        ->afterStateUpdated(fn ($state) => $this->customerId = $state ?: ''),
                ]),
            Wizard::make($this->getSteps())
                ->cancelAction($this->getCancelFormAction())
                ->submitAction($this->getSubmitFormAction())
                ->skippable($this->hasSkippableSteps()),
        ])
            ->columns(null);
    }

    protected function getSteps(): array
    {
        return [
            Step::make('Lookup Registration')
                ->columns(3)
                ->schema([
                    VRMInput::make('vrm')
                        ->required()
                        ->label('Registration number'),
                ])
                ->afterValidation(function (VehicleLookupService $lookupService, Get $get, Set $set): void {
                    try {
                        $vehicle = $lookupService->lookup($get('vrm'));
                        foreach ($vehicle->toArray() as $key => $value) {
                            $set($key, $value);
                        }
                    } catch (VehicleNotFoundException $e) {
                        // reset all vehicle values to null
                        foreach (array_diff(array_keys($get()), ['vrm', 'warranty', 'dealership_id', 'sales_person_id']) as $key) {
                            $set($key, null);
                        }
                        Notification::make()
                            ->warning()
                            ->title($e->getMessage())
                            ->send();
                    }
                }),
            Step::make('Vehicle Information')
                ->columns(3)
                ->schema([
                    TextInput::make('vrm')
                        ->label('VRM')
                        ->readOnly(),
                    VRMInput::make('private_plate'),
                    TextInput::make('vin')
                        ->label('VIN / Chassis Number')
                        ->required()
                        ->maxLength(40),
                    TextInput::make('vehicle_make')
                        ->required()
                        ->maxLength(100),
                    TextInput::make('vehicle_model')
                        ->required()
                        ->maxLength(100),
                    TextInput::make('vehicle_derivative')
                        ->maxLength(100),
                    TextInput::make('engine_capacity')
                        ->numeric(),
                    TextInput::make('vehicle_colour')
                        ->required()
                        ->maxLength(255),
                    Select::make('vehicle_type')
                        ->required()
                        ->options(VehicleType::toSelectArray())
                        ->in(VehicleType::cases()),
                    Select::make('fuel_type')
                        ->required()
                        ->options([
                            'DIESEL' => 'Diesel',
                            'PETROL' => 'Petrol',
                            'ELECTRICITY' => 'Electricity',
                            'HYBRID ELECTRIC' => 'Hybrid Petrol Electric',
                            'ELECTRIC DIESEL' => 'Hybrid Diesel Electric',
                        ])
                        ->in(fn (Select $component) => array_keys($component->getEnabledOptions())),
                    Select::make('transmission_type')
                        ->required()
                        ->options([
                            'MANUAL' => 'Manual',
                            'AUTOMATIC' => 'Automatic',
                        ])
                        ->in(fn (Select $component) => array_keys($component->getEnabledOptions())),
                    DatePicker::make('registration_date')
                        ->native(false)
                        ->displayFormat('d/m/Y')
                        ->required(),
                ]),
            Step::make('Sale Information')
                ->columns(3)
                ->schema([
                    Select::make('dealership_id')
                        ->relationship('dealership', 'name', function (Builder $query): void {
                            $query->where('account_id', auth()->user()->account_id);
                        })
                        ->default(function () {
                            if (auth()->user()->account->dealerships->count() === 1) {
                                return auth()->user()->account->dealerships->first()->id;
                            }

                            return null;
                        })
                        ->required(),
                    Select::make('sales_person_id')
                        ->visible(fn (): bool => auth()->user()->account->salesPeople->isNotEmpty())
                        ->default(function () {
                            if (auth()->user()->account->salesPeople->count() === 1) {
                                return auth()->user()->account->salesPeople->first()->id;
                            }

                            return null;
                        })
                        ->required()
                        ->relationship(
                            name: 'salesPerson',
                            titleAttribute: 'name',
                            modifyQueryUsing: fn (Builder $query) => $query->where('account_id', auth()->user()->account_id)
                        ),
                    DatePicker::make('start_date')
                        ->label('Vehicle collection / delivery date')
                        ->native(false)
                        ->displayFormat('d/m/Y')
                        ->minDate(auth()->user()->account->warranty_self_funded
                            ? null
                            : today()->subDays(15)->toDateString()
                        )
                        ->maxDate(auth()->user()->account->warranty_self_funded
                            ? null
                            : today()->addDays(30)->toDateString()
                        )
                        ->default(today()->toDateString())
                        ->required(),
                    TextInput::make('delivery_mileage')
                        ->required()
                        ->numeric()
                        ->rules(['between:0,1000000']),
                    TextInput::make('vehicle_price_paid')
                        ->required()
                        ->numeric()
                        ->rules(['between:1000,250000']),
                    DatePicker::make('last_service_date'),
                    TextInput::make('last_service_mileage')
                        ->numeric(),
                    Select::make('funding_method')
                        ->required()
                        ->options([
                            'cash' => 'Cash',
                            'finance' => 'Finance',
                            'lease' => 'Lease',
                        ])
                        ->in(fn (Select $component) => array_keys($component->getEnabledOptions())),
                ]),
        ];
    }

    protected function getCreatedNotification(): null
    {
        // Suppress the created notification as the user
        // now needs to add the products so is only part
        // way through the process.
        return null;
    }

    protected function getSubmitFormAction(): Action
    {
        return parent::getSubmitFormAction()->label('Next');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['account_id'] = auth()->user()->account_id;
        $data['sold_by_id'] = auth()->id();
        $data['customer_id'] = $this->customerId;

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return static::getResource()::getUrl('add-products', ['record' => $this->record]);
    }
}
