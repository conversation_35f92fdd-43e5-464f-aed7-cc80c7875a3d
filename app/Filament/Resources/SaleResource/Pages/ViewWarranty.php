<?php

namespace App\Filament\Resources\SaleResource\Pages;

use App\Actions\CancelWarranty;
use App\Filament\Resources\CustomerResource;
use App\Filament\Resources\SaleResource;
use App\Models\Sale;
use App\Models\Warranty;
use Filament\Actions\Action;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Navigation\NavigationItem;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Str;

class ViewWarranty extends ViewRecord
{
    protected static string $resource = SaleResource::class;

    protected static ?string $title = 'Warranty';

    public function getTitle(): string|Htmlable
    {
        return view('components.headers.warranty', ['warranty' => $this->record->warranty]);
    }

    public static function canAccess(array $parameters = []): bool
    {
        if (! $parameters['record']->warranty()->exists()) {
            return false;
        }

        return parent::canAccess($parameters);
    }

    public static function getNavigationIcon(): string|Htmlable|null
    {
        return null;
    }

    public static function getNavigationItems(array $urlParameters = []): array
    {
        return [
            NavigationItem::make(static::getNavigationLabel())
                ->group(static::getNavigationGroup())
                ->parentItem(static::getNavigationParentItem())
                ->icon(static::getNavigationIcon())
                ->activeIcon(static::getActiveNavigationIcon())
                ->isActiveWhen(fn (): bool => request()->routeIs(static::getRouteName()))
                ->sort(static::getNavigationSort())
                ->badge(static::getNavigationBadge(), color: static::getNavigationBadgeColor())
                ->badge($urlParameters['record']->warranty->status->label(), color: $urlParameters['record']->warranty->status->color())
                ->url(static::getNavigationUrl($urlParameters)),
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('cancel_warranty')
                ->visible(fn (Sale $sale) => Gate::allows('cancel', $sale->warranty))
                ->icon('heroicon-o-x-circle')
                ->requiresConfirmation()
                ->action(function (Sale $sale, CancelWarranty $cancelWarranty, Action $action) {
                    $cancelWarranty->execute($sale->warranty);
                    $action->success();
                })
                ->successNotification(fn (Sale $sale) => Notification::make('Warranty Cancelled')
                    ->body('The warranty has been successfully cancelled.')
                    ->sendToDatabase($sale->dealership)
                ),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema(fn (Sale $record) => [
                CustomerResource::getCustomerDetailsSection($record->customer)->relationship('customer'),
                $this->getInfoListSection($record->warranty),
                Section::make('Pricing')
                    ->columns(6)
                    ->schema([
                        TextEntry::make('warranty.provision')->label('Provision')->numeric(2)->prefix('£')->placeholder('N/A'),
                        TextEntry::make('warranty.selling_price')->label('Selling price')->numeric(2)->prefix('£')->placeholder('N/A'),
                        TextEntry::make('warranty.admin_fee')->label('Admin fee')->numeric(2)->prefix('£')->placeholder('N/A'),
                        TextEntry::make('warranty.sales_vat')->label('Sales VAT')->numeric(2)->prefix('£')->placeholder('N/A'),
                        TextEntry::make('warranty.monthly_selling_price')->label('Monthly Price')->numeric(2)->prefix('£')->placeholder('N/A'),
                        TextEntry::make('warranty.monthly_admin_fee')->label('Monthly Admin Fee')->numeric(2)->prefix('£')->placeholder('N/A'),
                        TextEntry::make('warranty.monthly_provision')->label('Monthly Provision')->numeric(2)->prefix('£')->placeholder('N/A'),
                    ]),
            ]);
    }

    public static function getInfoListSection(Warranty $warranty): Section
    {
        return Section::make('Warranty Information')
            ->columns(4)
            ->schema([
                TextEntry::make('warranty.status')
                    ->label('Status')
                    ->badge()
                    ->color($warranty->status->color()),
                TextEntry::make('warranty.sale.dealership.name')->label('Dealership'),
                TextEntry::make('warranty.product.coverLevel.name')->label('Product'),
                TextEntry::make('warranty.product.period')
                    ->label('Term')
                    ->formatStateUsing(function () use ($warranty): string {
                        if ($warranty->product->is_recurring) {
                            return 'Rolling monthly';
                        }

                        return $warranty->product->period.' '.Str::plural('month', $warranty->product->period);
                    }),
                IconEntry::make('warranty.is_self_funded')->label('Self Funded')->boolean(),
                TextEntry::make('warranty.annual_mileage_limit')->label('Annual Mileage Limit')->numeric(),
                TextEntry::make('warranty.mileage_cutoff')->label('Mileage Cutoff')->numeric(),
                TextEntry::make('warranty.total_claim_limit')->label('Total Claim Limit')->numeric(),
                TextEntry::make('warranty.individual_claim_limit')->label('Individual Claim Limit'),
                TextEntry::make('warranty.start_date')->label('Start Date')->date(),
                TextEntry::make('warranty.end_date')->label('End Date')->date()->placeholder('N/A'),
                TextEntry::make('warranty.account.authorisation_contact')->label('Authorisation Contact'),
                TextEntry::make('warranty.account.max_hourly_labour_rate')->label('Max Hourly Labour Rate')
                    ->numeric(2)->prefix('£')->placeholder('N/A'),
            ]);
    }

    public function getRelationManagers(): array
    {
        return [
            SaleResource\RelationManagers\WarrantyClaimsRelationManager::class,
        ];
    }
}
