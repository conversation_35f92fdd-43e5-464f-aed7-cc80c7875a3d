<?php

namespace App\Filament\Resources;

use App\Actions\Voip\GetCallControlExtensions;
use App\Filament\Resources\UserResource\Pages;
use App\Models\User;
use Closure;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Models\Role;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    public static function canAccess(): bool
    {
        if (Auth::user()->isViewingAllRecords() && ! Auth::user()->hasPermissionTo('admin-users.manage')) {
            return false;
        }

        return parent::canAccess();
    }

    public static function getNavigationLabel(): string
    {
        return Auth::user()->isViewingAllRecords() ? 'Admin Users' : 'Users';
    }

    public static function getBreadcrumb(): string
    {
        return static::getNavigationLabel();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->columns(2)
                    ->schema([
                        Forms\Components\TextInput::make('first_name')
                            ->required()
                            ->maxLength(25),
                        Forms\Components\TextInput::make('last_name')
                            ->required()
                            ->maxLength(25),
                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(50),
                        Forms\Components\Select::make('role_id')
                            ->multiple()
                            ->relationship(
                                name: 'roles',
                                titleAttribute: 'name',
                                modifyQueryUsing: fn ($query) => $query->where('is_internal', Auth::user()->isViewingAllRecords())
                            )
                            ->preload()
                            ->required()
                            ->rules([
                                fn (): Closure => function (string $attribute, $value, Closure $fail) {
                                    $allowedRoles = Role::where('is_internal', Auth::user()->isViewingAllRecords())->pluck('id');

                                    if (! collect($value)->every(fn (string $roleId) => $allowedRoles->contains($roleId))) {
                                        $fail('The :attribute field contains an invalid role.');
                                    }
                                },

                                fn (): Closure => function (string $attribute, $value, Closure $fail) {
                                    if (Auth::user()->isViewingAllRecords() && ! collect($value)->contains((string) Role::findByName('Admin')->id)) {
                                        $fail('The :attribute field must have the Admin role.');
                                    }
                                },
                            ]),
                        Forms\Components\Checkbox::make('create_sales_person')
                            ->hidden(Auth::user()->isViewingAllRecords())
                            ->visibleOn('create')
                            ->label('Create Sales Person')
                            ->helperText('If this person sells vehicles, select this option to automatically create a sales person record.'),
                        Forms\Components\Grid::make()
                            ->visible(Auth::user()->isViewingAllRecords())
                            ->relationship('voipUser', fn ($state) => (bool) $state['number'])
                            ->schema([
                                Forms\Components\Select::make('number')
                                    ->label('Voip Phone User')
                                    ->native(false)
                                    ->options(fn (GetCallControlExtensions $getCallControlExtensions) => $getCallControlExtensions->execute()),
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('last_name')
                    ->label('Name')
                    ->getStateUsing(fn (User $record) => $record->last_name.', '.$record->first_name)
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable(),
                Tables\Columns\TextColumn::make('accounts.name')
                    ->hidden(Auth::user()->isViewingAllRecords())
                    ->listWithLineBreaks()
                    ->searchable(),
                Tables\Columns\TextColumn::make('roles.name')
                    ->hidden(Auth::user()->isViewingAllRecords())
                    ->listWithLineBreaks()
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->when(
            Auth::user()->isViewingAllRecords(),
            fn ($query) => $query->admin(),
            fn ($query) => $query->dealer()->where('account_id', Auth::user()->account_id)
        );
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
            'view' => Pages\ViewUser::route('/{record}'),
        ];
    }
}
