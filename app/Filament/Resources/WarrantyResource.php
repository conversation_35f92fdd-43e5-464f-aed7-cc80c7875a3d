<?php

namespace App\Filament\Resources;

use App\Filament\Filters\DateRangeFilter;
use App\Filament\Resources\WarrantyResource\Pages;
use App\Filament\Tables\Columns\CustomerColumn;
use App\Filament\Tables\Columns\ProductStatusColumn;
use App\Filament\Tables\Columns\VehicleColumn;
use App\Models\Warranty;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class WarrantyResource extends Resource
{
    protected static ?string $model = Warranty::class;

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->with([
            'sale.customer',
            'sale.manufacturer',
        ]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Toggle::make('is_self_funded')
                    ->disabled()
                    ->required(),
                Forms\Components\TextInput::make('provision')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('selling_price')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('admin_fee')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('monthly_selling_price')
                    ->numeric()
                    ->hint('Gross'),
                Forms\Components\TextInput::make('monthly_admin_fee')
                    ->numeric()
                    ->hint('NET'),
                Forms\Components\TextInput::make('monthly_provision')
                    ->numeric(),
                Forms\Components\TextInput::make('vat')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('sales_vat')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('annual_mileage_limit')
                    ->numeric(),
                Forms\Components\TextInput::make('individual_claim_limit')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('total_claim_limit')
                    ->required()
                    ->numeric(),
                Forms\Components\DatePicker::make('start_date')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('sale.id')
                    ->label('Sale #')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Date entered')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('sale.dealership.short_name')
                    ->label('Dealership')
                    ->toggleable()
                    ->visible(Auth::user()->isViewingAllRecords() || Auth::user()->account?->dealerships->count() > 1)
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_self_funded')
                    ->label('Self Funded')
                    ->boolean()
                    ->alignCenter()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('product.coverLevel.name')
                    ->description(fn (Warranty $warranty) => sprintf('%s months', $warranty->product->period))
                    ->label('Product')
                    ->sortable(),
                Tables\Columns\TextColumn::make('sale.start_date')
                    ->label('Start Date')
                    ->date()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('end_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('sale.salesPerson.name')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->sortable(),

                CustomerColumn::make('sale.customer'),

                VehicleColumn::make('vehicle'),

                ProductStatusColumn::make('status')
                    ->recurringBadge(fn (Warranty $warranty) => $warranty->isRecurring()),
                Tables\Columns\IconColumn::make('product.is_recurring')
                    ->label('Subscription')
                    ->sortable()
                    ->boolean()
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('provision')
                    ->money()
                    ->sortable()
                    ->alignRight()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('selling_price')
                    ->money()
                    ->sortable()
                    ->alignRight()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('admin_fee')
                    ->money()
                    ->sortable()
                    ->alignRight()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('monthly_selling_price')
                    ->money()
                    ->sortable()
                    ->alignRight()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('monthly_admin_fee')
                    ->money()
                    ->sortable()
                    ->alignRight()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('monthly_provision')
                    ->money()
                    ->sortable()
                    ->alignRight()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('vat')
                    ->money()
                    ->sortable()
                    ->alignRight()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('sales_vat')
                    ->money()
                    ->sortable()
                    ->alignRight()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('annual_mileage_limit')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('individual_claim_limit')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('total_claim_limit')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                DateRangeFilter::make('end_date'),
                SelectFilter::make('account')->relationship('account', 'name'),
                SelectFilter::make('product')
                    ->relationship('product.coverLevel', 'name', fn ($query) => $query->orderBy('id')),
            ], layout: FiltersLayout::AboveContent)
            ->filtersFormColumns(3)
            ->actions([
                Tables\Actions\ViewAction::make()->url(fn (Warranty $warranty) => SaleResource::getUrl('view-warranty', [$warranty->sale_id])),
                //                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWarranties::route('/'),
            'burn-rates' => Pages\ListWarrantyBurnRateReport::route('/burn-rates'),
            'edit' => Pages\EditWarranty::route('/{record}/edit'),
        ];
    }
}
