<?php

namespace App\Filament\Resources\SalesLeadResource\Pages;

use App\Enums\CallOutcome;
use App\Filament\Resources\SalesLeadResource;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListSalesLeads extends ListRecords
{
    protected static string $resource = SalesLeadResource::class;

    public function getTabs(): array
    {
        return [
            'all' => $this->makeTab(),
            'new-leads' => $this->makeTab(fn (Builder $query) => $query->doesntHave('callOutcomes')),
            'callbacks' => $this->makeTab(fn (Builder $query) => $query->having('latest_callback_date', '<=', today()->endOfDay())),
            'upcoming-callbacks' => $this->makeTab(fn (Builder $query) => $query->having('latest_callback_date', '>', today()->endOfDay())),
            'not-interested' => $this->makeTab(fn (Builder $query) => $query->having('latest_call_outcome', CallOutcome::NOT_INTERESTED->value)),
        ];
    }

    protected function makeTab(?\Closure $callback = null)
    {
        $callback = $callback ?: fn (Builder $query) => $query;

        return Tab::make()
            ->modifyQueryUsing($callback)
            ->badge(fn (Builder $query) => $callback(static::getResource()::getEloquentQuery())->count())
            ->badgeColor('success');
    }
}
