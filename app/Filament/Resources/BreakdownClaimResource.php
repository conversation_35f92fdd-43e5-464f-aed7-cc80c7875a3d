<?php

namespace App\Filament\Resources;

use App\Enums\ProductStatus;
use App\Filament\Resources\BreakdownClaimResource\Pages;
use App\Models\BreakdownClaim;
use App\Models\Sale;
use App\Services\Tax\VatCalculator;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\Summarizers\Average;
use Filament\Tables\Columns\Summarizers\Range;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class BreakdownClaimResource extends Resource
{
    protected static ?string $model = BreakdownClaim::class;

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->whereHas('plan');
    }

    public static function form(Form $form, ?Sale $sale = null): Form
    {
        $sale = $sale ?: $form->getRecord()->plan->sale;

        return $form
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\Select::make('entered_by_id')
                            ->disabled()
                            ->hiddenOn('create')
                            ->relationship('enteredBy', 'last_name')
                            ->getOptionLabelFromRecordUsing(fn (Model $user) => $user->getNameAttribute())
                            ->required(),
                        Forms\Components\TextInput::make('claim_number')
                            ->hiddenOn('create')
                            ->required()
                            ->disabled(),
                        Forms\Components\DatePicker::make('failure_date')
                            ->label('Date of Failure')
                            ->helperText(function () use ($sale) {
                                if ($sale->breakdownPlan->status === ProductStatus::EXPIRED) {
                                    return 'This breakdown plan has expired.';
                                }

                                return '';
                            })
                            ->date()
                            ->native(false)
                            ->displayFormat('d/m/Y')
                            ->maxDate(now())
                            ->required(),
                        Forms\Components\TextInput::make('failure_mileage')
                            ->label('Mileage at Failure')
                            ->helperText(fn () => sprintf(
                                'The mileage cutoff for this warranty is %s miles (based on an annual mileage of %s miles per year)',
                                number_format($sale->warranty->mileage_cutoff),
                                number_format($sale->warranty->annual_mileage_limit)
                            ))
                            ->numeric()
                            ->required(),
                        Forms\Components\TextInput::make('reference')
                            ->required()
                            ->helperText('Your reference for this claim'),
                        Forms\Components\Textarea::make('cause')
                            ->label('Cause of Failure')
                            ->required()
                            ->helperText('Please describe the reason for the callout'),
                        Forms\Components\Section::make('Costs')
                            ->columnStart(2)
                            ->schema([
                                Forms\Components\TextInput::make('cost')
                                    ->label('Cost (net)')
                                    ->prefix('£')
                                    ->numeric()
                                    ->lazy()
                                    ->afterStateUpdated(function (VatCalculator $vatCalculator, callable $get, callable $set) {
                                        $set('cost', number_format($get('cost'), 2, '.', ''));
                                        $set('vat', number_format($vatCalculator->getVatAmount($get('cost')), 2, '.', ''));
                                        $set('cost_gross', number_format($vatCalculator->addVat($get('cost')), 2, '.', ''));
                                    })
                                    ->afterStateHydrated(function (VatCalculator $vatCalculator, callable $get, callable $set) {
                                        $set('cost_gross', number_format($vatCalculator->addVat($get('cost')), 2, '.', ''));
                                    })
                                    ->required(),
                                Forms\Components\TextInput::make('vat')
                                    ->label('VAT')
                                    ->prefix('£')
                                    ->readOnly()
                                    ->numeric(),
                                Forms\Components\TextInput::make('cost_gross')
                                    ->label('Cost (gross)')
                                    ->prefix('£')
                                    ->readOnly()
                                    ->dehydrated(false)
                                    ->numeric(),
                            ]),
                    ]),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('failure_date', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('claim_number')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('plan.sale.dealership.short_name')
                    ->label('Dealership')
                    ->toggleable()
                    ->visible(fn () => Auth::user()->isViewingAllRecords() || Auth::user()->account?->dealerships->count() > 1)
                    ->sortable(),
                Tables\Columns\TextColumn::make('failure_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('enteredBy.name')
                    ->sortable(),
                Tables\Columns\TextColumn::make('reference')
                    ->alignCenter()
                    ->searchable(),
                Tables\Columns\TextColumn::make('plan.sale.vrm')
                    ->label('VRM')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('plan.sale.vehicle_details')
                    ->label('Vehicle')
                    ->searchable(true, function (Builder $query, $search) {
                        return $query->whereHas('plan.sale', fn ($q) => $q->search($search));
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('failure_mileage')
                    ->numeric()
                    ->sortable()
                    ->alignRight()
                    ->summarize([
                        Average::make(),
                        Range::make(),
                    ]),
                Tables\Columns\TextColumn::make('cost')
                    ->label('Cost (net)')
                    ->money()
                    ->alignRight()
                    ->sortable()
                    ->summarize([
                        Average::make(),
                        Range::make(),
                    ]),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make()->slideOver(),
                Tables\Actions\EditAction::make()->slideOver(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBreakdownClaims::route('/'),
        ];
    }
}
