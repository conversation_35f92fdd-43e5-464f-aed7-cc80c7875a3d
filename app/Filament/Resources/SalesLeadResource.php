<?php

namespace App\Filament\Resources;

use App\Enums\CallOutcome;
use App\Filament\Resources\SalesLeadResource\Pages;
use App\Filament\Resources\SalesLeadResource\RelationManagers\CallOutcomesRelationManager;
use App\Filament\Resources\SalesLeadResource\RelationManagers\SalesOffersRelationManager;
use App\Filament\Tables\Columns\CustomerColumn;
use App\Filament\Tables\Columns\VehicleColumn;
use App\Filament\Widgets\CustomerCallActions;
use App\Filament\Widgets\CustomerInformation;
use App\Filament\Widgets\WarrantyInformation;
use App\Livewire\ListPhoneCalls;
use App\Models\SalesLead;
use App\Models\SalesLeadCallOutcome;
use Filament\Infolists\Components\Livewire;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class SalesLeadResource extends Resource
{
    protected static ?string $model = SalesLead::class;

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->whereHas('sale')
            ->with([
                'sale.customer',
                'sale.manufacturer',
            ])
            ->addSelect([
                'latest_call_outcome' => SalesLeadCallOutcome::select('outcome')
                    ->whereColumn('sales_lead_id', 'sales_leads.id')
                    ->latest('id')
                    ->take(1),
            ])
            ->addSelect([
                'latest_callback_date' => SalesLeadCallOutcome::select('callback_date')
                    ->whereColumn('sales_lead_id', 'sales_leads.id')
                    ->latest('id')
                    ->take(1),
            ])
            ->withCasts([
                'latest_call_outcome' => CallOutcome::class,
                'latest_callback_date' => 'datetime',
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            // Sort by latest callback date with null values last
            ->defaultSort(fn (Builder $query) => $query->orderByRaw('-latest_callback_date DESC')->orderBy('id'))
            ->columns([
                Tables\Columns\TextColumn::make('sale.id')
                    ->label('Sale #')
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('sale.dealership.short_name')
                    ->label('Dealership')
                    ->toggleable()
                    ->visible(Auth::user()->isViewingAllRecords() || Auth::user()->account?->dealerships->count() > 1)
                    ->sortable(),
                Tables\Columns\TextColumn::make('sale.start_date')
                    ->label('Start Date')
                    ->date()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                CustomerColumn::make('sale.customer'),

                VehicleColumn::make('vehicle'),

                Tables\Columns\TextColumn::make('latest_call_outcome')
                    ->label('Current Status')
                    ->placeholder('Not called')
                    ->badge()
                    ->color(fn (CallOutcome $state) => $state->color())
                    ->alignCenter()
                    ->badge(),

                Tables\Columns\TextColumn::make('latest_callback_date')
                    ->label('Callback Date')
                    ->placeholder('N/A')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([

            ], layout: FiltersLayout::AboveContent)
            ->filtersFormColumns(3)
            ->actions([
            ])
            ->bulkActions([
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist->columns(3)
            ->schema(fn (SalesLead $record) => [
                Livewire::make(CustomerInformation::class, ['customer' => $record->sale->customer])
                    ->key('customer-information')
                    ->columnSpan(2),
                Livewire::make(CustomerCallActions::class, ['salesLead' => $record])
                    ->key('customer-call-actions'),
                Livewire::make(WarrantyInformation::class, ['sale' => $record->sale])
                    ->key('warranty-information')
                    ->columnSpanFull(),
                Livewire::make(ListPhoneCalls::class, ['customer' => $record->sale->customer])
                    ->key('phone-calls')
                    ->columnSpanFull(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            CallOutcomesRelationManager::class,
            SalesOffersRelationManager::class,
        ];
    }

    public static function getNavigationItems(): array
    {
        return [
            parent::getNavigationItems()[0]
                ->isActiveWhen(fn () => request()->routeIs(static::getRouteBaseName().'.*') && ! request()->routeIs(static::getRouteBaseName().'.calendar')),
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSalesLeads::route('/'),
            'calendar' => Pages\ViewSalesLeadCalendar::route('/calendar'),
            'view' => Pages\ViewSalesLead::route('/{record}'),
        ];
    }
}
