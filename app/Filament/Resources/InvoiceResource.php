<?php

namespace App\Filament\Resources;

use App\Filament\Resources\InvoiceResource\Pages;
use App\Filament\Resources\InvoiceResource\RelationManagers\InvoiceLineItemsRelationManager;
use App\Filament\Resources\InvoiceResource\RelationManagers\PaymentsRelationManager;
use App\Models\ClaimEstimate;
use App\Models\Invoice;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class InvoiceResource extends Resource
{
    protected static ?string $model = Invoice::class;

    protected static ?string $navigationIcon = 'invoices';

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('invoice_number', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('account.short_name')
                    ->label('Account')
                    ->toggleable()
                    ->visible(fn () => Auth::user()->isViewingAllRecords())
                    ->sortable(),
                Tables\Columns\TextColumn::make('date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('due_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('period_start')
                    ->date()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('period_end')
                    ->date()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        Invoice::STATUS_DELETED, Invoice::STATUS_VOIDED => 'danger',
                        Invoice::STATUS_PENDING, Invoice::STATUS_SUBMITTED, Invoice::STATUS_AUTHORISED => 'warning',
                        Invoice::STATUS_PAID => 'success',
                        default => 'info',
                    }),
                Tables\Columns\TextColumn::make('invoice_number')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('line_items_sum_total')
                    ->label('Amount')
                    ->sum('lineItems', 'total')
                    ->alignRight()
                    ->money()
                    ->sortable(),
                Tables\Columns\TextColumn::make('invoiceable_type')->toggleable(isToggledHiddenByDefault: true)
                    ->label('Entity type')
                    ->searchable(),
                Tables\Columns\TextColumn::make('payments_count')
                    ->label('Payments')
                    ->alignCenter()
                    ->counts('payments')
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('payments_sum_amount')
                    ->label('Paid')
                    ->sum(['payments' => fn (Builder $query) => $query->paid()], 'amount')
                    ->alignCenter()
                    ->money()
                    ->sortable(),
                Tables\Columns\TextColumn::make('emailed_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make()
                    ->schema([
                        Grid::make()
                            ->columns(['lg' => 5])
                            ->schema([
                                TextEntry::make('date')->date(),
                                TextEntry::make('due_date')->date(),
                                TextEntry::make('invoice_number'),
                                TextEntry::make('status')
                                    ->badge()
                                    ->formatStateUsing(fn (Invoice $invoice): string => strtoupper(str_replace('_', ' ', $invoice->status)))
                                    ->color(fn (string $state): string => match ($state) {
                                        Invoice::STATUS_DELETED, Invoice::STATUS_VOIDED => 'danger',
                                        Invoice::STATUS_PENDING, Invoice::STATUS_SUBMITTED, Invoice::STATUS_AUTHORISED => 'warning',
                                        Invoice::STATUS_PAID => 'success',
                                        default => 'info',
                                    }),
                                TextEntry::make('period')
                                    ->getStateUsing(function (Invoice $invoice): string {
                                        if (! $invoice->period_start || ! $invoice->period_end) {
                                            return '';
                                        }

                                        return $invoice->period_start->format('d/m/Y').' - '.$invoice->period_end->format('d/m/Y');
                                    })
                                    ->placeholder('N/A'),

                            ]),
                        Grid::make()
                            ->columns(['lg' => 5])
                            ->visible(fn (Invoice $invoice): bool => (bool) $invoice->invoiceable)
                            ->schema([
                                TextEntry::make('invoice_to')
                                    ->columnSpan(2)
                                    ->getStateUsing(function (Invoice $invoice) {
                                        return [
                                            $invoice->invoiceable->getName(),
                                            ...explode('|||', $invoice->invoiceable->fullAddress('|||')),
                                        ];
                                    })
                                    ->url(fn (Invoice $invoice) => $invoice->invoiceable->getUrl())
                                    ->listWithLineBreaks(),
                                TextEntry::make('claimAuthorisation.estimate')
                                    ->visible(fn ($state) => $state)
                                    ->label('Warranty Claim')
                                    ->formatStateUsing(fn (ClaimEstimate $state) => $state->claim->reference)
                                    ->url(fn (ClaimEstimate $state) => ClaimResource::getUrl('view-estimate', ['claim' => $state->claim, 'record' => $state])),
                                TextEntry::make('total')
                                    ->label('Invoice Total')
                                    ->getStateUsing(fn (Invoice $invoice) => $invoice->getTotal())
                                    ->money()
                                    ->placeholder('N/A'),
                                TextEntry::make('paid_total')
                                    ->getStateUsing(fn (Invoice $invoice) => $invoice->payments()->paid()->sum('amount'))
                                    ->money()
                                    ->placeholder('N/A'),
                                TextEntry::make('description')->columnSpan(5),
                            ]),
                    ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            InvoiceLineItemsRelationManager::class,
            PaymentsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInvoices::route('/'),
            'view' => Pages\ViewInvoice::route('/{record}'),
        ];
    }
}
