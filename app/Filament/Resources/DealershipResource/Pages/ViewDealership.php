<?php

namespace App\Filament\Resources\DealershipResource\Pages;

use App\Filament\Resources\DealershipResource;
use App\Filament\Widgets\MissingDirectDebitAlert;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;

class ViewDealership extends ViewRecord
{
    protected static string $resource = DealershipResource::class;

    protected function getHeaderActions(): array
    {
        return [
            EditAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            MissingDirectDebitAlert::make(['record' => $this->getRecord()]),
        ];
    }
}
