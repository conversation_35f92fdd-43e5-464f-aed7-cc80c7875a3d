<?php

namespace App\Filament\Resources;

use App\Enums\SupportChannel;
use App\Enums\SupportPriority;
use App\Filament\Resources\SupportTicketResource\Pages;
use App\Models\Customer;
use App\Models\SupportTicket;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\FontWeight;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Auth;

class SupportTicketResource extends Resource
{
    protected static ?string $model = SupportTicket::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->columns()
                    ->schema([
                        Forms\Components\TextInput::make('createdBy.name')
                            ->label('Created by')
                            ->hiddenOn('create')
                            ->formatStateUsing(fn (?SupportTicket $record) => $record?->createdBy?->name)
                            ->disabled(),
                        Forms\Components\Select::make('assigned_to_id')
                            ->required()
                            ->default(Auth::id())
                            ->relationship(
                                name: 'assignedTo',
                                modifyQueryUsing: fn ($query) => $query
                                    ->when(Auth::user()->account_id, fn ($query, $accountId) => $query->where('account_id', $accountId))
                            )
                            ->getOptionLabelFromRecordUsing(fn (User $record) => $record->name)
                            ->searchable(['first_name', 'last_name', 'email'])
                            ->preload(),
                        Forms\Components\Select::make('customer_id')
                            ->live()
                            ->relationship(
                                name: 'customer',
                                modifyQueryUsing: fn ($query) => $query
                                    ->when(Auth::user()->account_id, fn ($query, $accountId) => $query->where('account_id', $accountId))
                            )
                            ->getOptionLabelFromRecordUsing(fn (Customer $record) => $record->getName())
                            ->searchable(['first_name', 'last_name', 'email'])
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                $customer = Customer::find($state);
                                $set('name', $customer->getName());
                                $set('email', $customer->email);
                                $set('phone', $customer->phone);
                            }),
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('email')
                            ->lazy()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                $customer = Customer::where('email', $state)
                                    ->where('account_id', Auth::user()->account_id)
                                    ->first();
                                if ($customer) {
                                    $set('customer_id', $customer->id);
                                    $set('name', $customer->getName());
                                    $set('phone', $customer->phone);
                                }
                            })
                            ->email()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('phone')
                            ->tel()
                            ->maxLength(255),
                        Forms\Components\Hidden::make('status')
                            ->default('open'),
                        Forms\Components\Select::make('channel')
                            ->required()
                            ->options(SupportChannel::toSelectArray()),
                        Forms\Components\Select::make('priority')
                            ->required()
                            ->options(SupportPriority::toSelectArray()),
                        Forms\Components\TextInput::make('type')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('subject')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->required()
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                //                Tables\Columns\TextColumn::make('inbound_email_id')
                //                    ->numeric()
                //                    ->sortable(),

                Tables\Columns\Layout\Stack::make([
                    Tables\Columns\Layout\Split::make([
                        Tables\Columns\TextColumn::make('id')->weight(FontWeight::Bold),
                        Tables\Columns\TextColumn::make('name')
                            ->searchable(),
                        Tables\Columns\TextColumn::make('priority')
                            ->alignCenter()
                            ->badge()
                            ->formatStateUsing(fn (SupportPriority $state) => $state->label())
                            ->sortable(),
                    ]),
                    Tables\Columns\Layout\Split::make([
                        Tables\Columns\TextColumn::make('status')
                            ->alignCenter()
                            ->badge()
                            ->searchable(),
                        Tables\Columns\TextColumn::make('channel')
                            ->alignCenter()
                            ->badge(),
                        Tables\Columns\TextColumn::make('created_at')
                            ->dateTime()
                            ->sortable(),
                    ]),
//                    Tables\Columns\TextColumn::make('createdBy.name')
//                        ->sortable(),
                    Tables\Columns\TextColumn::make('assignedTo.name')
                        ->sortable(),



                    Tables\Columns\TextColumn::make('type')
                        ->searchable(),
                    Tables\Columns\TextColumn::make('subject')
                        ->columnSpanFull()
                        ->searchable(),
                    Tables\Columns\TextColumn::make('description')
                        ->inline(false)
                        ->columnSpanFull(),

                ]),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->contentGrid([
                'md' => 2,
                'xl' => 3,
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSupportTickets::route('/'),
            'create' => Pages\CreateSupportTicket::route('/create'),
            'edit' => Pages\EditSupportTicket::route('/{record}/edit'),
        ];
    }
}
