<?php

namespace App\Filament\Tables\Columns;

use App\Models\BreakdownPlan;
use App\Models\Customer;
use App\Models\Sale;
use App\Models\ServicePlan;
use App\Models\Warranty;
use Filament\Tables\Columns\Column;

class CustomerColumn extends Column
{
    protected string $view = 'filament.tables.columns.customer-column';

    public function configure(): static
    {
        $wheres = fn ($q, $search) => $q->where(fn ($q) => $q
            ->orWhere('phone', 'like', $search.'%')
            ->orWhere('email', 'like', $search.'%')
            ->orWhere('first_name', 'like', '%'.$search.'%')
            ->orWhere('last_name', 'like', '%'.$search.'%')
        );

        return $this
            ->label('Customer')
            ->searchable(
                true,
                function ($query, $search) use ($wheres) {
                    $model = $query->getModel();

                    return match (get_class($model)) {
                        Customer::class => $wheres($query, $search),
                        Sale::class => $query->whereHas('customer', fn ($q) => $wheres($q, $search)),
                        Warranty::class, BreakdownPlan::class, ServicePlan::class => $query->whereHas('sale.customer', fn ($q) => $wheres($q, $search)),
                    };
                });
    }

    public function getCustomer()
    {
        $record = $this->getRecord();

        return match (get_class($record)) {
            Customer::class => $record,
            Sale::class => $record->customer,
            Warranty::class, BreakdownPlan::class, ServicePlan::class => $record->sale->customer,
            default => throw new \RuntimeException('Invalid record type'),
        };
    }
}
