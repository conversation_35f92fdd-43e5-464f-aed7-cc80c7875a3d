<?php

namespace App\Filament\Tables\Columns;

use App\Models\BreakdownPlan;
use App\Models\Sale;
use App\Models\SalesLead;
use App\Models\ServicePlan;
use App\Models\Warranty;
use Filament\Tables\Columns\Column;
use Illuminate\Database\Eloquent\Model;

class VehicleColumn extends Column
{
    protected string $view = 'filament.tables.columns.vehicle-column';

    public function getSale(): ?Model
    {
        $record = $this->getRecord();

        return match (get_class($record)) {
            Warranty::class, BreakdownPlan::class, ServicePlan::class, SalesLead::class => $record->sale,
            Sale::class => $record,
            default => throw new \RuntimeException('Invalid record type'),
        };
    }

    public function configure(): static
    {
        $wheres = fn ($q, $search) => $q->where(fn ($q) => $q
            ->orWhere('vrm', 'like', '%'.$search.'%')
            ->orWhere('private_plate', 'like', '%'.$search.'%')
            ->orWhere('vehicle_make', 'like', '%'.$search.'%')
            ->orWhere('vehicle_model', 'like', '%'.$search.'%')
        );

        return $this
            ->searchable(
                true,
                function ($query, $search) use ($wheres) {
                    if ($query->getModel() instanceof Sale) {
                        return $wheres($query, $search);
                    }

                    return $query->whereHas('sale', fn ($q) => $wheres($q, $search));
                });
    }
}
