<?php

namespace App\Filament\Widgets;

use App\Filament\Resources\SaleResource\Pages\ListSales;
use App\Models\Sale;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class OperationsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            $this->getStat('Customers needing Direct Debit', $this->salesNeedingDirectDebit(), 'needs-direct-debit'),
            $this->getStat('Customers with failed subscription payments', $this->salesWithFailedSubscriptionPayments(), 'failed-subscription-payments'),
        ];
    }

    protected function getStat(string $label, int $count, string $filterStatusValue)
    {
        return Stat::make($label, $count)
            ->description($count > 0 ? 'Action Required' : 'All clear - Relax')
            ->descriptionIcon($count > 0 ? 'heroicon-m-exclamation-triangle' : 'heroicon-m-check-circle')
            ->color($count > 0 ? 'danger' : 'success')
            ->url(ListSales::getUrl([
                'tableFilters' => [
                    'status' => [
                        'value' => $filterStatusValue,
                    ],
                ],
            ]));
    }

    protected function salesNeedingDirectDebit(): int
    {
        return Sale::query()
            ->needsDirectDebit()
            ->count();
    }

    public function salesWithFailedSubscriptionPayments(): int
    {
        return Sale::query()
            ->withFailedSubscriptionPayments()
            ->count();
    }
}
