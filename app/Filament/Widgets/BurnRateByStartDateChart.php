<?php

namespace App\Filament\Widgets;

use App\Models\Warranty;
use App\Models\WarrantyBurnRateView;
use Carbon\Carbon;
use Filament\Widgets\ChartWidget;
use Flowframe\Trend\Trend;
use Flowframe\Trend\TrendValue;

class BurnRateByStartDateChart extends ChartWidget
{
    protected static ?string $heading = 'Burn Rate by Start Date';

    protected static ?string $pollingInterval = null;

    protected int|string|array $columnSpan = 'full';

    protected static ?string $maxHeight = '250px';

    protected static ?array $options = [
        'scales' => [
            'Y' => [
                'type' => 'linear',
                'display' => true,
                'position' => 'left',
                'title' => [
                    'display' => true,
                    'text' => '£',
                ],
            ],
            'Y2' => [
                'type' => 'linear',
                'display' => true,
                'position' => 'right',
                'title' => [
                    'display' => true,
                    'text' => 'Burn Rate %',
                ],
                'grid' => [
                    'drawOnChartArea' => false, // Prevents grid lines from overlapping
                ],
            ],
        ],
    ];

    protected function getData(): array
    {
        $revenueTrend = Trend::query(WarrantyBurnRateView::query())
            ->dateColumn('start_date')
            ->between(
                Carbon::parse(Warranty::min('start_date'))->startOfMonth(),
                Carbon::now()->subMonth()->endOfMonth(),
            )
            ->perMonth()
            ->sum('earned_revenue');

        $claimTrend = Trend::query(WarrantyBurnRateView::query())
            ->dateColumn('start_date')
            ->between(
                Carbon::parse(Warranty::min('start_date'))->startOfMonth(),
                Carbon::now()->subMonth()->endOfMonth(),
            )
            ->perMonth()
            ->sum('claims_total_net');

        return [
            'datasets' => [
                [
                    'label' => 'Earned Revenue (£)',
                    'data' => $revenueTrend->pluck('aggregate')->toArray(),
                    'backgroundColor' => '#36A2EB',
                    'borderColor' => '#9BD0F5',
                    'yAxisID' => 'Y',
                ],
                [
                    'label' => 'Total Claims (£)',
                    'data' => $claimTrend->pluck('aggregate')->toArray(),
                    'backgroundColor' => '#FFCE56',
                    'borderColor' => '#FFCE56',
                    'yAxisID' => 'Y',
                ],
                [
                    'label' => 'Burn Rate',
                    'data' => $claimTrend
                        ->pluck('aggregate')
                        ->map(function ($value, $key) use ($revenueTrend) {
                            $revenue = $revenueTrend->get($key)->aggregate;
                            if ($revenue === 0) {
                                return 0;
                            }

                            return $value / $revenueTrend->get($key)->aggregate * 100;
                        })
                        ->toArray(),
                    'backgroundColor' => '#FF6384',
                    'borderColor' => '#FF6384',
                    'yAxisID' => 'Y2',
                ],
            ],
            'labels' => $revenueTrend->map(fn (TrendValue $trendValue) => Carbon::parse($trendValue->date)->format('M y'))->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }
}
