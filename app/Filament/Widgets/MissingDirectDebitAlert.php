<?php

namespace App\Filament\Widgets;

use App\Actions\GenerateQrCode;
use App\Actions\SetupBillingRequest;
use App\Models\Concerns\DirectDebitableContract;
use App\Models\Dealership;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Contracts\HasInfolists;
use Filament\Infolists\Infolist;
use Filament\Widgets\Widget;
use Livewire\Attributes\Computed;

class MissingDirectDebitAlert extends Widget implements HasForms, HasInfolists
{
    use InteractsWithForms;
    use InteractsWithInfolists;

    protected static string $view = 'filament.widgets.missing-direct-debit-alert';

    protected int|string|array $columnSpan = 'full';

    public DirectDebitableContract $record;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                ImageEntry::make('qr_code')
                    ->label('Scan QR Code')
                    ->hint('Scan this QR code from a phone to setup Direct Debit')
                    ->getStateUsing(function (Dealership $dealership, GenerateQrCode $generateQrCode) {
                        if ($this->record->getMandateUrl()) {
                            return $generateQrCode->execute($this->record->getMandateUrl());
                        }
                    })
                    ->width('200')
                    ->height('200'),
            ]);
    }

    #[Computed]
    public function requiresMandateSetup(): bool
    {
        return $this->record->requiresMandateSetup();
    }

    #[Computed]
    public function getDirectDebitUrl(): ?string
    {
        return $this->record->getMandateUrl();
    }

    public function openModal(SetupBillingRequest $setupBillingRequest): void
    {
        $setupBillingRequest->execute($this->record, sendNotification: false);

        $this->dispatch('open-modal', id: 'direct-debit-mandate-modal');
    }

    public function sendEmail(SetupBillingRequest $setupBillingRequest): void
    {
        $setupBillingRequest->execute($this->record, sendNotification: true);
    }
}
