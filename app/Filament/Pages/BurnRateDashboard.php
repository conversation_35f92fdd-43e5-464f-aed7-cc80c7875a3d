<?php

namespace App\Filament\Pages;

use App\Filament\Widgets\RetainedProvisionOverview;
use App\Filament\Widgets\SelfFundedFilter;
use Filament\Pages\Dashboard\Concerns\HasFiltersForm;
use Illuminate\Support\Facades\Auth;

class BurnRateDashboard extends BaseDashboard
{
    use HasFiltersForm;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    public static function canAccess(): bool
    {
        if (Auth::user()->isDealer() && ! Auth::user()->account->warranty_self_funded) {
            return false;
        }

        return parent::canAccess();
    }

    public function getVisibleWidgets(): array
    {
        return [
            SelfFundedFilter::make(),
            RetainedProvisionOverview::make([
                'heading' => 'All Time',
            ]),
            RetainedProvisionOverview::make([
                'heading' => 'Active Warranties',
                'additionalFilters' => ['expired' => false],
            ]),
            RetainedProvisionOverview::make([
                'heading' => 'Expired Warranties',
                'additionalFilters' => ['expired' => true],
            ]),
        ];
    }
}
