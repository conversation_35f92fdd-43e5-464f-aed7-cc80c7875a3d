<?php

namespace App\Livewire;

use App\Models\Account;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class TenantSwitcher extends Component implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill([
            'account_id' => Auth::user()->account_id,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('account_id')
                    ->hiddenLabel()
                    ->reactive()
                    ->afterStateUpdated(function ($state) {
                        Auth::user()->update(['account_id' => $state]);
                        $this->redirect('/', navigate: true);
                    })
                    ->options(
                        Account::query()
                            ->when(! Auth::user()->isAdmin(), function ($query) {
                                $query->whereHas('users', fn ($q) => $q->where('users.id', Auth::user()->id));
                            })
                            ->pluck('name', 'id')
                    )
                    ->optionsLimit(20)
                    ->searchable()
                    ->searchPrompt('Type account name')
                    ->placeholder('Showing all accounts'),
            ])
            ->statePath('data');
    }

    public function render()
    {
        return view('livewire.tenant-switcher');
    }
}
