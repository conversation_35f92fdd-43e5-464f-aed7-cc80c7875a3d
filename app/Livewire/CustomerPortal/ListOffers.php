<?php

namespace App\Livewire\CustomerPortal;

use App\Models\SalesOffer;
use Livewire\Attributes\Computed;
use Livewire\Component;

class ListOffers extends Component
{
    #[Computed]
    public function offers()
    {
        return SalesOffer::query()
            ->withWhereHas('salesLead.sale', fn ($q) => $q->where('customer_id', auth()->id()))
            ->with([
                'warrantyProduct.coverLevel',
                //                'breakdownProduct',
                //                'servicePlanProduct',
            ])
            ->get();
    }

    public function render()
    {
        return view('livewire.customer-portal.list-offers');
    }
}
