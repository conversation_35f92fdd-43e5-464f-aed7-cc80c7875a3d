<?php

namespace App\Livewire;

use App\Actions\SetupBillingRequest;
use App\Events\BillingRequestFulfilled;
use App\Models\BillingRequest;
use App\Models\Payment;
use App\Services\Payments\AccessPaysuitePaymentProcessor;
use App\Services\Payments\DirectDebitData;
use Cs278\BankModulus\BankModulus;
use Illuminate\Validation\ValidationException;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

class DirectDebitForm extends Component
{
    public ?string $accountHolderName = null;

    public string $sortCode = '';

    public string $accountNumber = '';

    public bool $canAuthorise = true;

    public string $step = '';

    public BillingRequest $billingRequest;

    public array $rules = [
        'accountHolderName' => 'required|max:255',
        'sortCode' => 'required|size:6',
        'accountNumber' => 'required|min:6|max:8',
    ];

    public function mount(SetupBillingRequest $setupBillingRequest)
    {
        $this->billingRequest->update(['visited_at' => now()]);

        $directDebitable = $this->billingRequest->getDirectDebitable();

        if ($this->billingRequest->isExpired() && $directDebitable) {
            $setupBillingRequest->execute($directDebitable, sendNotification: true);
        }

        $this->accountHolderName = $this->billingRequest->getAccountHolderName();

        $this->step = match (true) {
            $this->billingRequest->isExpired() => 'expired',
            $this->billingRequest->status === 'pending' => 'collect-details',
            default => 'completed',
        };
    }

    #[Computed]
    public function companyName(): string
    {
        return config('app.name');
    }

    protected function prepareForValidation($attributes)
    {
        $attributes['sortCode'] = str_replace('-', '', $attributes['sortCode']);
        $attributes['accountNumber'] = str_replace(' ', '', $attributes['accountNumber']);

        return $attributes;
    }

    public function validate($rules = null, $messages = [], $attributes = [])
    {
        $validData = parent::validate($rules, $messages, $attributes);

        $modulus = new BankModulus;

        if ($modulus->check($this->sortCode, $this->accountNumber) === false) {
            throw ValidationException::withMessages([
                'accountNumber' => 'Invalid account sort code or account number.',
            ]);
        }

        return $validData;
    }

    public function submitForm()
    {
        $this->validate();

        $modulus = new BankModulus;
        $modulus->normalize($this->sortCode, $this->accountNumber);

        $this->step = 'confirm';
    }

    public function confirm(AccessPaysuitePaymentProcessor $paymentProcessor)
    {
        $this->validate();

        $directDebitData = new DirectDebitData(
            accountHolderName: $this->accountHolderName,
            sortCode: $this->sortCode,
            accountNumber: $this->accountNumber,
        );

        $mandate = $paymentProcessor->setupMandate($this->billingRequest->getDirectDebitable(), $directDebitData);

        $this->billingRequest->fill([
            'account_holder_name' => $directDebitData->accountHolderName,
            'sort_code' => $directDebitData->sortCode,
            'account_number' => $directDebitData->accountNumber,
            'direct_debit_reference' => $mandate->directDebitReference,
            'provider_customer_id' => $mandate->paymentProcessorCustomerId,
            'mandate_id' => $mandate->paymentProcessorMandateId,
            'status' => Payment::STATUS_PENDING_SUBMISSION,
            'mandate_activated_at' => $mandate->activatedAt,
        ])->save();

        BillingRequestFulfilled::dispatch($this->billingRequest);

        $this->step = 'completed';
    }

    #[Title('Setup your Direct Debit')]
    public function render()
    {
        return view('livewire.direct-debit-form')->layout('components.layouts.auth');
    }
}
