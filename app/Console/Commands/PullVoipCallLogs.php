<?php

namespace App\Console\Commands;

use App\Jobs\PullCallLogs;
use App\Models\PhoneCall;
use Carbon\Carbon;
use Illuminate\Console\Command;

class PullVoipCallLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:pull-voip-call-logs {start?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Pull call logs from VoIP service';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $start = Carbon::parse($this->argument('start') ?: PhoneCall::query()->max('started_at'));
        $end = today()->endOfDay();

        for ($date = $start->copy(); $date->lte($end); $date->addDay()) {
            $this->info("Getting calls for {$date->format('Y-m-d')}");
            PullCallLogs::dispatchSync($date);
        }
    }
}
