<?php

namespace App\Console\Commands;

use App\Services\Accounting\AccountData;
use App\Services\Accounting\AccountingService;
use Illuminate\Console\Command;

class SetupNominalAccountsInXero extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'accounting:setup-nominal-accounts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup Nominal Accounts In Xero';

    /**
     * Execute the console command.
     */
    public function handle(AccountingService $accountingService): void
    {
        /** @var AccountData $accountData */
        foreach ($this->accountCollection() as $accountData) {
            try {
                /** @var AccountData $found */
                //                $found = $accountingService->getAccounts([
                // //                    'where' => sprintf('Code=="%s"', $accountData->code),
                //                ])->first();
                $found = false;
                if ($found) {
                    $this->info(sprintf('Updating account %s (%s)', $found->name, $found->code));
                    $success = $accountingService->updateAccount($found->id, $accountData);
                } else {
                    $this->info(sprintf('Creating account %s (%s)', $accountData->name, $accountData->code));
                    $success = $accountingService->createAccount($accountData);
                }
                $success ? $this->info("Success\n") : $this->error("Failed\n");
            } catch (\Throwable $e) {
                $this->error(sprintf(
                    'Error on account %s (%s): %s',
                    $accountData->name,
                    $accountData->code,
                    $e->getMessage()
                ));
            }
        }
    }

    protected function accountCollection(): array
    {
        return collect(config('accounting.nominal_codes'))
            ->flatten(1)
            ->map(function ($data) {
                return new AccountData(
                    id: null,
                    code: $data['code'],
                    name: $data['name'],
                    description: $data['description'] ?? null,
                    type: $data['type'],
                    class: $data['class'],
                    reportingCode: null,
                    reportingCodeName: null,
                    taxType: null,
                );
            })->all();
    }
}
