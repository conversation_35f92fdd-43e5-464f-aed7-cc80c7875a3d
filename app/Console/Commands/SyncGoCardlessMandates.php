<?php

namespace App\Console\Commands;

use App\Models\BillingRequest;
use App\Services\Payments\GoCardlessPaymentProcessor;
use Carbon\Carbon;
use Illuminate\Console\Command;

class SyncGoCardlessMandates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'gocardless:sync-mandates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync all mandates from the GoCardless account with the database';

    /**
     * Execute the console command.
     */
    public function handle(GoCardlessPaymentProcessor $goCardlessPaymentProcessor): void
    {
        BillingRequest::query()
            ->each(function (BillingRequest $billingRequest) use ($goCardlessPaymentProcessor) {
                $this->info('Processing billing request: '.$billingRequest->id);
                if ($billingRequest->provider_billing_request_id) {
                    $gcBillingRequest = $goCardlessPaymentProcessor->getClient()->billingRequests()->get($billingRequest->provider_billing_request_id);
                    if ($gcBillingRequest->status === 'pending') {
                        if (Carbon::parse($gcBillingRequest->created_at)->addDays(7)->isPast()) {
                            $billingRequest->delete();
                            $this->info('Deleting expired billing request: '.$billingRequest->id);
                            $this->info('-----------------------------------');

                            return;
                        }
                    }

                    if ($gcBillingRequest->status === 'fulfilled') {
                        $billingRequest->mandate_url = null;
                        $billingRequest->expires_at = null;
                    }
                    $billingRequest->mandate_id = $gcBillingRequest->links->mandate_request_mandate ?? null;
                    $billingRequest->save();
                    $this->info('Billing request updated from GC Billing Request: '.$billingRequest->id);
                    if ($billingRequest->mandate_id) {
                        $this->processMandate($billingRequest, $goCardlessPaymentProcessor);
                    }
                } else {
                    $this->processMandate($billingRequest, $goCardlessPaymentProcessor);
                }

                $this->info('-----------------------------------');
            });
    }

    protected function processMandate(BillingRequest $billingRequest, GoCardlessPaymentProcessor $goCardlessPaymentProcessor)
    {
        $gcMandate = $goCardlessPaymentProcessor->getClient()->mandates()->get($billingRequest->mandate_id);
        if ($gcMandate->status === 'cancelled') {
            $billingRequest->delete();
            $this->info('Deleting cancelled billing request: '.$billingRequest->id);

            return;
        }
        $billingRequest->status = $gcMandate->status;
        $billingRequest->mandate_url = null;
        $billingRequest->expires_at = null;
        $billingRequest->save();
        $this->info('Billing request updated from GC Mandate: '.$billingRequest->id);
    }
}
