<?php

namespace App\Console\Commands;

use App\Actions\PayLater\PullPayLaterPlansAction;
use Illuminate\Console\Command;

class PullPayLaterPlans extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pay-later:pull-plans';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Pull available payment plans from Pay Later API and store them in the database';

    /**
     * Execute the console command.
     */
    public function handle(PullPayLaterPlansAction $pullPayLaterPlansAction): int
    {
        $this->info('Pulling payment plans from Pay Later API...');

        $pullPayLaterPlansAction->execute();

        $this->info('Payment plans successfully updated');

        return 0;
    }
}
