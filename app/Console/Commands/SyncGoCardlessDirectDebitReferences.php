<?php

namespace App\Console\Commands;

use App\Models\BillingRequest;
use App\Services\Payments\GoCardlessPaymentProcessor;
use Illuminate\Console\Command;

class SyncGoCardlessDirectDebitReferences extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'gocardless:sync-direct-debit-references';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync direct debit references from GoCardless for all billing requests';

    /**
     * Execute the console command.
     */
    public function handle(GoCardlessPaymentProcessor $goCardlessPaymentProcessor): void
    {
        BillingRequest::query()
            ->where('provider', 'go_cardless')
            ->whereNotNull('mandate_id')
            ->whereNull('direct_debit_reference')
            ->each(function (BillingRequest $billingRequest) use ($goCardlessPaymentProcessor) {
                $this->info("Processing billing request: {$billingRequest->id}");

                try {
                    $mandate = $goCardlessPaymentProcessor->getMandate($billingRequest->mandate_id);

                    $billingRequest->update([
                        'direct_debit_reference' => $mandate->directDebitReference,
                        'mandate_activated_at' => $mandate->activatedAt,
                    ]);

                    $this->info("Updated direct debit reference for billing request: {$billingRequest->id}");
                } catch (\Exception $e) {
                    $this->error("Failed to process billing request {$billingRequest->id}: {$e->getMessage()}");
                    if (app()->isLocal()) {
                        throw $e;
                    }
                }

                $this->info('-----------------------------------');
            });
    }
}
