<?php

namespace App\Console\Commands;

use App\Models\Sale;
use App\Models\SalesLead;
use App\Services\LeadGenerator\LeadGeneratorContract;
use App\Services\LeadGenerator\WarrantyRenewalLeadGenerator;
use App\Services\LeadGenerator\WarrantyUpsellLeadGenerator;
use Illuminate\Console\Command;

class GenerateSalesLeads extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-sales-leads';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run all queries and generate sales leads';

    protected $generators = [
        WarrantyRenewalLeadGenerator::class,
        WarrantyUpsellLeadGenerator::class,
    ];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        foreach ($this->generators as $generatorClass) {
            $generator = $this->factory($generatorClass);

            $salesLeads = $generator
                ->query(Sale::query()->whereDoesntHave('salesLead'))
                ->get()
                ->map(fn (Sale $sale) => [
                    'upselling_sale_id' => $sale->getKey(),
                    'generator_class' => class_basename($generatorClass),
                    'assigned_to_user_id' => null,
                ])
                ->all();

            $count = SalesLead::query()->upsert($salesLeads, ['upselling_sale_id']);

            $this->info("{$generator->description()}: inserted {$count} sales leads.");
        }
    }

    private function factory(mixed $generatorClass): LeadGeneratorContract
    {
        return app($generatorClass);
    }
}
