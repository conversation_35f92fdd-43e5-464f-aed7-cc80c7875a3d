<?php

namespace App\Services\Payments;

use App\Enums\PaymentProvider;
use App\Models\Concerns\DirectDebitableContract;
use App\Models\Payment;

interface PaymentProcessor
{
    public function getProcessorIdentifier(): PaymentProvider;

    public function setupBillingRequestForCustomer(DirectDebitableContract $directDebitable): BillingRequestData;

    public function getMandate(string $mandateId): MandateData;

    public function createPayment(Payment $payment): PaymentData;

    public function getPayment(Payment $payment): PaymentData;
}
