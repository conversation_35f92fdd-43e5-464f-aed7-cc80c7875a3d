<?php

namespace App\Services\Payments;

use App\Enums\PaymentProvider;
use App\Models\Concerns\DirectDebitableContract;
use App\Models\Payment;

class MultiProviderPaymentProcessor implements PaymentProcessor
{
    private ?PaymentProcessor $selectedProcessor = null;

    public function __construct(private readonly array $config, private readonly array $processors) {}

    public function selectProcessor(PaymentProvider|string $paymentProcessor): PaymentProcessor
    {
        if (is_string($paymentProcessor)) {
            $paymentProcessor = PaymentProvider::from($paymentProcessor);
        }

        return $this->selectedProcessor = array_find($this->processors, fn (PaymentProcessor $processor) => $processor->getProcessorIdentifier() === $paymentProcessor);
    }

    public function defaultProcessor(): PaymentProcessor
    {
        return $this->selectProcessor($this->config['default']);
    }

    public function selectedProcessor(): ?PaymentProcessor
    {
        return $this->selectedProcessor;
    }

    public function getProcessorIdentifier(): PaymentProvider
    {
        return $this->defaultProcessor()->getProcessorIdentifier();
    }

    public function getMandate(string $mandateId): MandateData
    {
        return $this->selectedProcessor()->getMandate($mandateId);
    }

    public function createPayment(Payment $payment): PaymentData
    {
        return $this->selectProcessor($payment->provider)->createPayment($payment);
    }

    public function getPayment(Payment $payment): PaymentData
    {
        return $this->selectProcessor($payment->provider)->getPayment($payment);
    }

    public function setupBillingRequestForCustomer(DirectDebitableContract $directDebitable): BillingRequestData
    {
        return $this->defaultProcessor()->setupBillingRequestForCustomer($directDebitable);
    }
}
