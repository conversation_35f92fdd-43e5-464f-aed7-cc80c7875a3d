<?php

namespace App\Services\Payments\PayLater;

use App\Enums\PaymentProvider;
use App\Models\Account;
use App\Models\Customer;
use App\Models\Invoice;
use App\Models\PayLaterAgreement;
use App\Models\PayLaterPlan;
use App\Services\Payments\DirectDebit\CustomerData;
use Carbon\Carbon;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Http;

class PaymentAssistProcessor implements PayLaterPaymentProcessor
{
    const string LIVE_API_ENDPOINT = 'https://api.v1.payment-assist.co.uk';

    const string STAGING_API_ENDPOINT = 'https://api.demo.payassi.st';

    protected bool $isLive;

    protected string $apiKey;

    protected string $apiSecret;

    /**
     * @throws RequestException|\Illuminate\Http\Client\ConnectionException
     */
    public function sendRequest(string $endpoint, string $method = 'GET', array $options = [])
    {
        $options = [
            ...$options,
            'api_key' => $this->apiKey,
            'signature' => $this->generateSignature($options),
        ];

        try {
            return Http::baseUrl(
                $this->isLive ? self::LIVE_API_ENDPOINT : self::STAGING_API_ENDPOINT
            )
                ->throw()
                ->{strtolower($method)}($endpoint, $options)
                ->json();
        } catch (RequestException $e) {
            if (app()->isLocal()) {
                dd($method, $endpoint, $options, $e->response->getStatusCode(), $e->response->getBody()->getContents(), $e->response->headers());
            }
            throw $e;
        }
    }

    public function forAccount(Account $account): static
    {
        $serviceCredentials = $account->serviceCredentials()
            ->where('provider', 'payment_assist')
//            ->where('is_live', app()->isProduction())
            ->first();

        if (! $serviceCredentials) {
            throw new \RuntimeException('Payment Assist credentials not found');
        }

        return $this->setCredentials(
            apiKey: $serviceCredentials->credentials['api_key'],
            apiSecret: $serviceCredentials->credentials['api_secret'],
            isLive: $serviceCredentials->is_live,
        );
    }

    public function setCredentials(string $apiKey, string $apiSecret, bool $isLive): static
    {
        $this->apiKey = $apiKey;
        $this->apiSecret = $apiSecret;
        $this->isLive = $isLive;

        return $this;
    }

    public function getProcessorIdentifier(): PaymentProvider
    {
        return PaymentProvider::PAYMENT_ASSIST;
    }

    public function getAccountDetails()
    {
        return $this->sendRequest('account');
    }

    public function getPlanBreakdown(PayLaterPlan $payLaterPlan, mixed $amount): PlanBreakdownData
    {
        $response = $this->sendRequest('plan', 'POST', [
            'plan_id' => $payLaterPlan->provider_plan_id,
            'amount' => (int) floor($amount * 100),
        ]);

        return new PlanBreakdownData(
            name: $response['data']['plan'],
            amount: $response['data']['amount'] / 100,
            interest: $response['data']['interest'] / 100,
            repayable: $response['data']['repayable'] / 100,
            schedule: collect($response['data']['schedule'])
                ->map(fn ($item) => new PlanBreakdownPaymentData(
                    date: $item['date'],
                    amount: $item['amount'] / 100,
                ))
                ->all(),
        );
    }

    public function preApprove(Customer $customer): PreApprovalData
    {
        $response = $this->sendRequest('preapproval', 'POST', [
            'f_name' => $customer->first_name,
            's_name' => $customer->last_name,
            'addr1' => $customer->address_1,
            'postcode' => $customer->postcode,
        ]);

        return new PreApprovalData(
            approved: $response['data']['approved'],
            message: $response['msg'] ?? null,
        );
    }

    public function startApplication(PayLaterAgreement $payLaterAgreement, string $reference): ApplicationRedirectData
    {
        /** @var CustomerData $customer */
        $customer = $payLaterAgreement->payable->toCustomerData();

        $response = $this->sendRequest('begin', 'POST', [
            'order_id' => $reference,
            'amount' => (int) floor($payLaterAgreement->loan_amount * 100),
            'f_name' => $customer->firstName,
            's_name' => $customer->lastName,
            'addr1' => $customer->line1,
            'addr2' => $customer->line2,
            'town' => $customer->city,
            'county' => $customer->county ?? '',
            'postcode' => $customer->postcode,
            'email' => $customer->email,
            'telephone' => $customer->phone,
            'reg_no' => $payLaterAgreement->payable->vrm,
            'description' => $payLaterAgreement->payable->description(),

            'plan_id' => $payLaterAgreement->payLaterPlan->provider_plan_id,
            'webhook_url' => route('webhooks.payment-assist'),
        ]);

        return new ApplicationRedirectData(
            provider: PaymentProvider::PAYMENT_ASSIST,
            token: $response['data']['token'],
            url: $response['data']['url'],
        );
    }

    public function updateApplication(string $token, ?string $reference = null, bool $expire = false): ApplicationData
    {
        $response = $this->sendRequest('update', 'POST', array_filter([
            'order_id' => $reference,
            'expiry' => $expire ? now()->toDateTimeString() : null,
        ]));

        return new ApplicationData(
            provider: PaymentProvider::PAYMENT_ASSIST,
            token: $response['data']['token'],
            signature: $response['data']['signature'],
        );
    }

    public function getApplicationStatus(string $token): ApplicationStatusData
    {
        $response = $this->sendRequest('status', 'GET', [
            'token' => $token,
        ]);

        return new ApplicationStatusData(
            token: $token,
            providerReference: $response['data']['pa_ref'] ?? null,
            status: $response['data']['status'],
            planId: $response['data']['plan_id'],
            requiresInvoice: $response['data']['requires_invoice'] ?? false,
            hasInvoice: $response['data']['has_invoice'] ?? false,
            lastAccessedAt: Carbon::parse($response['data']['last_accessed_at']),
        );
    }

    public function uploadInvoice(string $applicationId, Invoice $invoice): bool
    {
        try {
            // Payment Assist requires invoice details to be uploaded as a document
            // First, generate a PDF of the invoice
            $pdfContent = $this->generateInvoicePdf($invoice);

            // Then upload it to Payment Assist
            $this->sendRequest("application/{$applicationId}/document", 'POST', [
                'multipart' => [
                    [
                        'name' => 'type',
                        'contents' => 'invoice',
                    ],
                    [
                        'name' => 'document',
                        'contents' => $pdfContent,
                        'filename' => "invoice_{$invoice->invoice_number}.pdf",
                    ],
                ],
            ]);

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Generate a PDF for the invoice
     * This is a placeholder - implement your actual PDF generation logic
     */
    protected function generateInvoicePdf(Invoice $invoice): string
    {
        // This would be your actual PDF generation logic
        // For example, using a package like barryvdh/laravel-dompdf

        // Placeholder implementation
        return 'PDF content would go here';
    }

    protected function mapStatus(string $status): string
    {
        return match (strtolower($status)) {
            'new', 'pending' => 'pending',
            'in_progress' => 'in_progress',
            'approved' => 'approved',
            'completed' => 'completed',
            'paid' => 'paid',
            'declined' => 'declined',
            'cancelled' => 'cancelled',
            'expired' => 'expired',
            default => strtolower(str_replace(' ', '_', $status)),
        };
    }

    private function generateSignature(array $params)
    {
        ksort($params);
        $params = array_change_key_case($params, CASE_UPPER);

        $string = collect($params)
            ->except(['SIGNATURE', 'API_KEY'])
            ->map(fn ($v) => is_bool($v) ? ($v ? 'true' : 'false') : $v)
            ->map(fn ($v, $k) => sprintf('%s=%s&', $k, $v))
            ->join('');

        return hash_hmac('sha256', $string, $this->apiSecret, false);
    }
}
