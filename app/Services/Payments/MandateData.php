<?php

namespace App\Services\Payments;

use App\Models\Payment;
use Carbon\Carbon;

final class MandateData
{
    public function __construct(
        public ?string $status,
        public ?string $paymentProcessorMandateId,
        public ?string $paymentProcessorCustomerId,
        public ?string $directDebitReference,
        public ?string $activatedAt,
    ) {
        if ($this->activatedAt && Carbon::parse($this->activatedAt)->isFuture()) {
            // If access paysuite, behave the same way as GoCardless
            $this->status = Payment::STATUS_PENDING_SUBMISSION;
        }
    }
}
