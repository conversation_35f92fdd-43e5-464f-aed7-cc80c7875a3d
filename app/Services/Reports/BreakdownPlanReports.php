<?php

namespace App\Services\Reports;

use App\Models\Account;
use App\Models\BreakdownPlan;

class BreakdownPlanReports
{
    public function dashboard(?Account $account = null, $filters = []): array
    {
        return [
            'total' => [
                'sales' => $sales = $this->provision($account, $filters),
                'authorised_claims' => $authorisations = $this->authorisedClaims($account, $filters),
                'margin' => ['value' => round($sales->value - $authorisations->value, 2)],
            ],
            'current' => [
                'sales' => $sales = $this->provision($account, $filters + ['expired' => false]),
                'authorised_claims' => $authorisations = $this->authorisedClaims($account, $filters + ['expired' => false]),
                'margin' => ['value' => round($sales->value - $authorisations->value, 2)],
            ],
            'expired' => [
                'sales' => $sales = $this->provision($account, $filters + ['expired' => true]),
                'authorised_claims' => $authorisations = $this->authorisedClaims($account, $filters + ['expired' => true]),
                'margin' => ['value' => round($sales->value - $authorisations->value, 2)],
            ],
            '30_days' => [
                'sales' => $sales = $this->provision($account, $filters + [
                    'dates' => [today()->subDays(30), today()->subDay()],
                ]),
                'authorised_claims' => $authorisations = $this->authorisedClaims($account, $filters + [
                    'dates' => [today()->subDays(30), today()->subDay()],
                ]),
                'margin' => ['value' => round($sales->value - $authorisations->value, 2)],
            ],
            '30_days_previous' => [
                'sales' => $sales = $this->provision($account, $filters + [
                    'dates' => [today()->subDays(60), today()->subDays(31)],
                ]),
                'authorised_claims' => $authorisations = $this->authorisedClaims($account, $filters + [
                    'dates' => [today()->subDays(60), today()->subDays(31)],
                ]),
                'margin' => ['value' => round($sales->value - $authorisations->value, 2)],
            ],
        ];
    }

    private function provision(?Account $account = null, $filters = [])
    {
        return BreakdownPlan::query()
            ->notCancelled()
            ->withoutGlobalScope('tenant')
            ->selectRaw('SUM(provision) as value')
            ->selectRaw('COUNT(*) as count')
            ->when($account, fn ($q) => $q->where('account_id', $account->id))
            ->when($filters['dates'] ?? false, fn ($q, $dates) => $q
                ->whereDate('breakdown_plans.created_at', '>=', $dates[0])
                ->whereDate('breakdown_plans.created_at', '<=', $dates[1])
            )
            ->when(isset($filters['selfFunded']), fn ($q) => $q->where('is_self_funded', $filters['selfFunded']))
            ->when(isset($filters['expired']), fn ($q) => $q->expired($filters['expired']))
            ->first();
    }

    private function authorisedClaims(?Account $account = null, $filters = [])
    {
        return BreakdownPlan::query()
            ->notCancelled()
            ->withoutGlobalScope('tenant')
            ->selectRaw('SUM(cost) as value')
            ->selectRaw('COUNT(*) as count')
            ->join('breakdown_claims', 'breakdown_claims.breakdown_plan_id', '=', 'breakdown_plans.id')
            ->when($account, fn ($q) => $q->where('account_id', $account->id))
            ->when($filters['dates'] ?? false, fn ($q, $dates) => $q
                ->whereDate('breakdown_claims.created_at', '>=', $dates[0])
                ->whereDate('breakdown_claims.created_at', '<=', $dates[1])
            )
            ->when(isset($filters['selfFunded']), fn ($q) => $q->where('is_self_funded', $filters['selfFunded']))
            ->when(isset($filters['expired']), fn ($q) => $q->expired($filters['expired']))
            ->first();
    }
}
