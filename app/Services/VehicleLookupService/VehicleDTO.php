<?php

namespace App\Services\VehicleLookupService;

use App\Enums\VehicleType;
use Str;

class VehicleDTO
{
    public function __construct(
        public string $vrm,
        public string $registrationDate,
        public ?string $colour,
        public ?string $fuelType,
        public ?string $transmissionType,
        public ?int $engineCapacity,
        public string $make,
        public ?string $model = null,
        public ?string $derivative = null,
        public ?VehicleType $type = null,
        public ?string $vin = null,
        public ?int $previousOwners = null,
        public ?string $ownerStartDate = null,
    ) {
        $this->normalisedMake();
    }

    public static function fromMotorspecResult(mixed $result)
    {
        return new self(
            vrm: $result['registration'],
            registrationDate: $result['vehicle']['dvla']['regDate'],
            colour: $result['vehicle']['dvla']['colour'] ?? null,
            fuelType: $result['vehicle']['dvla']['fuel'] ?? null,
            transmissionType: $result['specsVehicle']['transmissionMode'] ?? null,
            engineCapacity: $result['vehicle']['dvla']['cc'] ?? null,
            make: $result['specsVehicle']['make'] ?? $result['vehicle']['combined']['make'] ?? null,
            model: $result['specsVehicle']['model'] ?? $result['vehicle']['combined']['model'] ?? null,
            derivative: $result['specsVehicle']['version'] ?? $result['vehicle']['combined']['version'] ?? null,
            type: match (strtoupper($result['vehicle']['dvla']['bodyClass'])) {
                'CAR' => VehicleType::CAR,
                // possible TODO split heavy goods (body = tipper) to a different vehicle type?
                'LIGHT VAN', 'HEAVY GOODS' => VehicleType::VAN,
                'MOTORCYCLE' => VehicleType::MOTORCYCLE,
                'MISCELLANEOUS' => match (strtoupper($result['vehicle']['dvla']['body'])) {
                    'MOTOR HOME/CARAVAN' => VehicleType::MOTORHOME,
                    default => null,
                },
                default => null,
            },
            vin: $result['vehicle']['dvla']['vin'],
            previousOwners: $result['vehicle']['keepers']['numberOfPrevious'] ?? null,
            ownerStartDate: $result['vehicle']['keepers']['startDate'] ?? null,
        );
    }

    public function toArray()
    {
        return [
            'vrm' => $this->vrm,
            'vin' => $this->vin,
            'vehicle_make' => $this->make,
            'vehicle_model' => $this->model,
            'vehicle_derivative' => $this->derivative,
            'vehicle_colour' => $this->colour,
            'vehicle_type' => $this->type?->value,
            'fuel_type' => $this->fuelType,
            'transmission_type' => $this->transmissionType,
            'engine_capacity' => $this->engineCapacity,
            'registration_date' => $this->registrationDate,
            'is_hybrid' => $this->isHybrid(),
            'is_pure_electric' => $this->isPureElectric(),
            'is_ice' => $this->isIce(),
        ];
    }

    public function isHybrid(): bool
    {
        return Str::contains(strtolower($this->fuelType), 'hybrid');
    }

    public function isPureElectric(): bool
    {
        return ! $this->isHybrid() && Str::contains(strtolower($this->fuelType), 'electric');
    }

    public function isIce(): bool
    {
        return ! $this->isHybrid() && ! $this->isPureElectric();
    }

    protected function normalisedMake(): void
    {
        $this->make = match (strtoupper($this->make)) {
            'MERCEDES-BENZ' => 'MERCEDES',
            'ROVER MG' => 'MG',
            default => $this->make,
        };
    }
}
