<?php

namespace App\Services\LeadGenerator;

use App\Models\Sale;
use Illuminate\Database\Eloquent\Builder;

class WarrantyUpsellLeadGenerator implements LeadGeneratorContract
{
    public function description(): string
    {
        return 'Targets all recent warranties which have not been extended by the dealer.';
    }

    public function hook(Sale $sale): string
    {
        return sprintf(
            '%s month standard warranty started on %s.',
            $sale->warranty->duration,
            $sale->warranty->starts_at->formatLocal()
        );
    }

    public function query(Builder $query): Builder
    {
        return $query->doesntHave('salesLead')
            ->whereHas('warranty', function ($q) {
                return $q
                    ->live()
                    ->whereBetween('start_date', [today()->subMonth(), today()])
                    ->whereNotNull('end_date')
                    ->where('selling_price', 0);
            });
    }
}
