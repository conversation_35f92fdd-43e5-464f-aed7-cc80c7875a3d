<?php

namespace App\Http\Controllers\CustomerPortal;

use App\Http\Controllers\Controller;

class WelcomeController extends Controller
{
    public function __invoke()
    {
        return view('customer-portal.index', [
            'customer' => auth()
                ->user()
                ->load([
                    'sales' => fn ($query) => $query->latest(),
                    'sales.warranty',
                    'sales.breakdownPlan',
                    'sales.servicePlan',
                ]),
        ]);
    }
}
