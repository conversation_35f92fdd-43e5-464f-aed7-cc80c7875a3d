<?php

namespace App\Http\Controllers;

use App\Actions\ProcessGoCardlessPaymentFromWebhook;
use App\Actions\ProcessGoCardlessPayoutFromWebhook;
use App\Events\BillingRequestFulfilled;
use App\Models\BillingRequest;
use App\Services\Payments\GoCardlessPaymentProcessor;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class GoCardlessWebhookController extends Controller
{
    public function __construct(public GoCardlessPaymentProcessor $goCardless) {}

    public function __invoke(Request $request)
    {
        $request->validate([
            'events' => 'required|array',
            'events.*.resource_type' => 'required|string',
            'events.*.action' => 'required|string',
            'events.*.details.cause' => 'required|string',
        ]);

        foreach ($request->input('events') as $event) {
            $method = Str::camel(strtolower($event['details']['cause']));
            if (method_exists($this, $method)) {
                $this->callMethod($method, $event);
            }

            $method = Str::camel('process_'.strtolower($event['resource_type']));
            if (method_exists($this, $method)) {
                $this->callMethod($method, $event);
            }
        }

        return response()->noContent();
    }

    protected function callMethod($method, $event)
    {
        try {
            app()->call([$this, $method], ['event' => $event]);
        } catch (\Throwable $e) {
            if (app()->environment('local')) {
                throw $e;
            }
            report($e);
        }
    }

    public function billingRequestFlowVisited($event)
    {
        $billingRequestId = $event['links']['billing_request'];

        $gcBillingRequest = $this->goCardless->getBillingRequest($billingRequestId);

        $billingRequest = BillingRequest::query()
            ->where('provider_billing_request_id', $billingRequestId)
            ->sole();

        $billingRequest->update([
            'status' => $gcBillingRequest->status,
            'visited_at' => Carbon::parse($event['created_at']),
        ]);
    }

    public function billingRequestFulfilled($event)
    {
        $billingRequestId = $event['links']['billing_request'];

        $gcBillingRequest = $this->goCardless->getBillingRequest($billingRequestId);

        $billingRequest = BillingRequest::query()
            ->where('provider_billing_request_id', $billingRequestId)
            ->sole();

        $billingRequest->update([
            'status' => $this->normaliseStatus($gcBillingRequest->status),
            'mandate_id' => $gcBillingRequest->links->mandate_request_mandate ?? null,
            'mandate_activated_at' => Carbon::parse($event['created_at']),
            'mandate_url' => null,
            'expires_at' => null,
        ]);

        BillingRequestFulfilled::dispatch($billingRequest);
    }

    public function mandateCancelled($event)
    {
        $this->handleMandate($event);

        // TODO - send a notification to dealer and or admin
    }

    public function mandateCreated($event)
    {
        $this->handleMandate($event);
    }

    protected function handleMandate($event)
    {
        $mandateId = $event['links']['mandate'] ?? null;
        $billingRequestId = $event['links']['billing_request'] ?? null;

        if ($mandateId) {
            $mandate = $this->goCardless->getMandate($mandateId);
            $status = $mandate->status;
        } else {
            $status = $this->goCardless->getBillingRequest($billingRequestId)->status;
        }

        $billingRequest = BillingRequest::query()
            ->where('mandate_id', $mandateId)
            ->orWhere('provider_billing_request_id', $billingRequestId)
            ->sole();

        $billingRequest->update([
            'status' => $this->normaliseStatus($status),
            'mandate_id' => $mandateId,
            'mandate_activated_at' => Carbon::parse($event['created_at']),
            'direct_debit_reference' => $mandate->directDebitReference ?? null,
            'mandate_url' => null,
            'expires_at' => null,
        ]);
    }

    public function processPayments(ProcessGoCardlessPaymentFromWebhook $processGoCardlessPayment, $event)
    {
        $processGoCardlessPayment->execute($event['links']['payment']);
    }

    public function processPayouts(ProcessGoCardlessPayoutFromWebhook $processGoCardlessPayout, $event)
    {
        $processGoCardlessPayout->execute($event['links']['payout']);
    }

    private function normaliseStatus(mixed $status)
    {
        return match ($status) {
            'fulfilled' => 'active',
            default => $status,
        };
    }
}
