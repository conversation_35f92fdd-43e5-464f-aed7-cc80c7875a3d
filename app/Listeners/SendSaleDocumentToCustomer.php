<?php

namespace App\Listeners;

use App\Events\SaleAdded;
use Illuminate\Contracts\Queue\ShouldQueue;

readonly class SendSaleDocumentToCustomer implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct() {}

    /**
     * Handle the event.
     */
    public function handle(SaleAdded $event): void
    {
        if ($event->sale->account->send_contract_emails && $event->sale->documentsCanBeGenerated()) {
            $event->sale->sendWelcomeAndDocumentNotification();
        }
    }
}
