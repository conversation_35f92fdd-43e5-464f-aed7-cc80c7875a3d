<?php

namespace App\Policies;

use App\Enums\ProductStatus;
use App\Models\Claim;
use App\Models\User;
use App\Models\Warranty;
use Illuminate\Auth\Access\HandlesAuthorization;

class ClaimPolicy
{
    use HandlesAuthorization;

    public function before(User $user): ?bool
    {
        if (! $user->isViewingAllRecords() &&
            $user->account->warrantyProducts()->doesntExist() &&
            Warranty::where('account_id', $user->account_id)->has('claims')->doesntExist()) {

            return false;
        }

        return null;
    }

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user): bool
    {
        return $user->can('claims.view');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, Claim $claim): bool
    {
        if (! $user->isViewingAllRecords() && $user->account_id !== $claim->warranty?->account_id) {
            return false;
        }

        return $user->can('claims.view');
    }

    /**
     * Determine whether the user can create models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user, ?Warranty $warranty = null): bool
    {
        if ($warranty && in_array($warranty->status, [ProductStatus::PENDING, ProductStatus::CANCELLED])) {
            return false;
        }

        return $user->can('claims.create');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, Claim $claim): bool
    {
        if (! $user->isViewingAllRecords() && $user->account_id !== $claim->warranty?->account_id) {
            return false;
        }
        if ($claim->estimates()->exists()) {
            return false;
        }
        if ($claim->rejection()->exists()) {
            return false;
        }

        return $user->can('claims.update');
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, Claim $claim): bool
    {
        if ($claim->estimates()->exists()) {
            return false;
        }
        if ($claim->rejection()->exists()) {
            return false;
        }

        return $user->can('claims.delete');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function fileUpload(User $user, Claim $claim): bool
    {
        return $user->can('claims.update');
    }

    public function exportAll(User $user): bool
    {
        return $user->can('claims.export');
    }
}
