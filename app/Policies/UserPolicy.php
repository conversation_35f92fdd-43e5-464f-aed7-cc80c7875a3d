<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class UserPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user): bool
    {
        return $user->can('users.manage');
    }

    /**
     * Determine whether the user can view this user.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, User $model): bool
    {
        return $user->can('users.manage');
    }

    /**
     * Determine whether the user can create models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user): bool
    {
        return $user->can('users.manage');
    }

    /**
     * Determine whether the user can update this user.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, User $model): bool
    {
        return $user->can('users.manage');
    }

    /**
     * Determine whether the user can delete this user.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, User $model): bool
    {
        return $user->can('users.manage');
    }

    /**
     * Determine whether the user can restore this user.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, User $model): bool
    {
        return $user->can('users.manage');
    }

    /**
     * Determine whether the user can permanently delete this user.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, User $model): bool
    {
        return false;
    }

    /**
     * Determine whether the user can view / attach / detach accounts to this user.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function manageAccounts(User $user, User $model)
    {
        if (! $user->can('users.manage')) {
            return false;
        }
        if ($model->can('users.manage')) {
            return false;
        }

        return true;
    }
}
