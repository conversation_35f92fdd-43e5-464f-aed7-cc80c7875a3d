<?php

namespace App\Policies;

use App\Enums\ProductStatus;
use App\Models\ServicePlan;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class ServicePlanPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user): bool
    {
        if (! $user->isViewingAllRecords() &&
            $user->account->servicePlanProducts()->doesntExist() &&
            ServicePlan::whereHas('account', fn ($query) => $query->where('id', $user->account_id))->doesntExist()) {

            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, ServicePlan $servicePlan): bool
    {
        if ($user->isAdmin()) {
            return true;
        }
        if ($user->account_id === $servicePlan->account_id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user): bool
    {
        return false;
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, ServicePlan $servicePlan): bool
    {
        return false;
        if ($user->isAdmin()) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can update the status of the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function cancel(User $user, ServicePlan $servicePlan)
    {
        if ($user->isAdmin() === false) {
            return false;
        }

        return in_array($servicePlan->status, [ProductStatus::PENDING, ProductStatus::LIVE]);
    }
}
