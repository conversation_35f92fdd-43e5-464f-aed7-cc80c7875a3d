<?php

namespace App\Actions;

use App\Models\Sale;
use App\Services\MotHistoryService\MotHistoryService;
use Carbon\Carbon;
use Spatie\QueueableAction\QueueableAction;

class FetchMotHistory
{
    use QueueableAction;

    public function __construct(private readonly MotHistoryService $motHistoryService) {}

    public function execute(Sale $sale): void
    {
        if ($sale->mot_last_checked_at && $sale->mot_last_checked_at->diffInDays() < 1) {
            return;
        }

        try {
            if ($sale->vin) {
                $motHistory = $this->motHistoryService->lookupVin($sale->vin);
            } else {
                $motHistory = $this->motHistoryService->lookupVrm($sale->vrm);
            }

            foreach ($motHistory['motTests'] ?? [] as $item) {
                $motTest = $sale->motTests()->firstOrCreate([
                    'test_number' => $item['motTestNumber'],
                ], [
                    'completed_at' => Carbon::parse($item['completedDate']),
                    'expiry_date' => $item['expiryDate'],
                    'odometer_reading' => $item['odometerValue'],
                    'odometer_unit' => $item['odometerUnit'],
                    'result' => $item['testResult'],
                    'data_source' => $item['dataSource'],
                ]);

                if ($motTest->wasRecentlyCreated) {
                    foreach ($item['defects'] as $defect) {
                        $motTest->defects()->create([
                            'type' => $defect['type'],
                            'description' => $defect['text'],
                            'dangerous' => $defect['dangerous'],
                        ]);
                    }
                }
            }
        } catch (\Exception $e) {
            if (app()->isLocal()) {
                throw $e;
            }
            report($e);
        }

        $sale->update([
            'mot_last_checked_at' => Carbon::now(),
        ]);
    }
}
