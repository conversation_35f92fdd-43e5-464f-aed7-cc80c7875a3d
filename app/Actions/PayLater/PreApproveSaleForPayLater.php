<?php

namespace App\Actions\PayLater;

use App\Models\Concerns\PayLaterContract;
use App\Models\PayLaterAgreement;
use App\Services\Payments\PayLater\PayLaterPaymentProcessor;

class PreApproveSaleForPayLater
{
    public function __construct(
        private readonly PayLaterPaymentProcessor $payLaterPaymentProcessor,
    ) {}

    public function execute(PayLaterContract $billable): PayLaterAgreement
    {
        if ($billable->getPayLaterAgreement()) {
            return $billable->getPayLaterAgreement();
        }

        $preApprovalData = $this->payLaterPaymentProcessor
            ->forAccount($billable->getAccount())
            ->preApprove($billable->getCustomer());

        return $billable->payLaterAgreement()
            ->create([
                'is_approved' => $preApprovalData->approved,
            ]);
    }
}
