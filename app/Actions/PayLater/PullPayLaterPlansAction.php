<?php

namespace App\Actions\PayLater;

use App\Models\Account;
use App\Services\Payments\PayLater\PaymentAssistProcessor;
use Illuminate\Support\Arr;

class PullPayLaterPlansAction
{
    public function __construct(private readonly PaymentAssistProcessor $paymentAssistProcessor) {}

    public function execute(Account $account): void
    {
        $accountDetails = $this->paymentAssistProcessor
            ->forAccount($account)
            ->getAccountDetails();

        if (! isset($accountDetails['data']['plans'])) {
            abort('No plans found in the API response');
        }

        $plans = $accountDetails['data']['plans'];

        foreach ($plans as $planData) {
            $account->payLaterPlans()->updateOrCreate(
                ['provider_plan_id' => $planData['plan_id']],
                Arr::except($planData, ['plan_id', 'commission_fixed_fee'])
            );
        }
    }
}
