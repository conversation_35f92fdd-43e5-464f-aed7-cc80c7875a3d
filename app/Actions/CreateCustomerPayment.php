<?php

namespace App\Actions;

use App\Models\InvoiceLineItem;
use App\Models\Payment;
use App\Models\Sale;
use App\Services\Tax\VatCalculator;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Illuminate\Support\Facades\DB;

class CreateCustomerPayment
{
    private Sale $sale;

    private Carbon|CarbonImmutable $chargeDate;

    private bool $isFirstPayment;

    public function __construct(
        private readonly VatCalculator $vatCalculator,
        private readonly CreatePaymentInProcessor $createPaymentInProcessor,
    ) {}

    public function execute(
        Sale $sale,
        Carbon|CarbonImmutable $chargeDate,
        bool $isFirstPayment = false,
        bool $isDraft = false,
    ): Payment {
        $this->sale = $sale;
        $this->chargeDate = $chargeDate;
        $this->isFirstPayment = $isFirstPayment;

        $payment = $this->updateOrCreatePaymentInDatabase();

        if (! $isDraft) {
            $this->createPaymentInProcessor->onQueue()->execute($payment);
        }

        return $payment;
    }

    private function updateOrCreatePaymentInDatabase(): Payment
    {
        $this->sale->load([
            'warranty',
            'servicePlan',
            'billingRequest',
        ]);

        return DB::transaction(function () {
            $payment = $this->sale->payments()->firstOrCreate([
                'billing_request_id' => $this->sale->billingRequest->id,
                'provider' => $this->sale->billingRequest->provider,
                'period_start' => $this->chargeDate,
            ], [
                'charge_date' => $this->chargeDate->clone()->startOfDay()->subDays(7)->isPast() ? null : $this->chargeDate,
                'status' => Payment::STATUS_DRAFT,
            ]);

            if ($this->sale->warranty?->isRecurring() && (! $this->isFirstPayment || $this->sale->warranty->selling_price == 0)) {
                // only add this line item for a sale with a recurring warranty, and in the case of the first payment
                // where a payment wasn't taken at the point of sale (by the dealer)

                $adminFeeVat = $this->vatCalculator->getVatAmount($this->sale->warranty->monthly_admin_fee);

                $payment->lineItems()->updateOrCreate([
                    'payable_type' => InvoiceLineItem::TYPE_WARRANTY,
                    'payable_id' => $this->sale->warranty->id,
                    'account_code' => $this->sale->warranty->nominalAccountCode(),
                ], [
                    'description' => "{$this->sale->id}: Warranty ".($this->sale->warranty->is_self_funded ? 'Self-Funded' : 'Managed Fund'),
                    'unit_amount' => $this->sale->warranty->monthly_selling_price - $this->sale->warranty->monthly_admin_fee - $adminFeeVat,
                    'tax' => 0,
                ]);
                $payment->lineItems()->updateOrCreate([
                    'payable_type' => InvoiceLineItem::TYPE_WARRANTY,
                    'payable_id' => $this->sale->warranty->id,
                    'account_code' => $this->sale->warranty->nominalAccountCodeForAdminFee(),
                ], [
                    'description' => "{$this->sale->id}: Warranty admin fee",
                    'unit_amount' => $this->sale->warranty->monthly_admin_fee,
                    'tax' => $adminFeeVat,
                ]);
            }
            if ($this->sale->servicePlan?->isRecurring()) {
                // only add this line item for a sale with a recurring service plan
                $payment->lineItems()->updateOrCreate([
                    'payable_type' => InvoiceLineItem::TYPE_SERVICE_PLAN,
                    'payable_id' => $this->sale->servicePlan->id,
                    'account_code' => $this->sale->servicePlan->nominalAccountCode(),
                ], [
                    'description' => "{$this->sale->id}: Service Plan liability held for dealer",
                    'unit_amount' => $this->sale->servicePlan->selling_price - $this->sale->servicePlan->admin_fee,
                    'tax' => 0,
                ]);
                $payment->lineItems()->updateOrCreate([
                    'payable_type' => InvoiceLineItem::TYPE_SERVICE_PLAN,
                    'payable_id' => $this->sale->servicePlan->id,
                    'account_code' => $this->sale->servicePlan->nominalAccountCodeForAdminFee(),
                ], [
                    'description' => "{$this->sale->id}: Service Plan admin fee",
                    'unit_amount' => $this->sale->servicePlan->admin_fee,
                    'tax' => $this->vatCalculator->getVatAmount($this->sale->servicePlan->admin_fee),
                ]);
            }

            $payment->amount = $payment->lineItems()->sum(DB::raw('unit_amount + tax'));
            $payment->save();

            return $payment;
        });
    }
}
