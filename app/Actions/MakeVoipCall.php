<?php

namespace App\Actions;

use App\Models\Concerns\VoipCallable;
use App\Models\User;
use App\Services\Voip\Voip3cxProvider;
use Illuminate\Support\Facades\Log;

class MakeVoipCall
{
    public function __construct(
        private readonly Voip3cxProvider $voipProvider
    ) {}

    public function execute(User $user, VoipCallable $voipCallable)
    {
        Log::info('Making call');

        $this->voipProvider->makeCall($user->voipUser->number, $voipCallable->getPhoneNumberForVoipCall());
    }
}
