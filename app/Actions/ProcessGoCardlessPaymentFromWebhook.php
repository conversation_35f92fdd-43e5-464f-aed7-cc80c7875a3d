<?php

namespace App\Actions;

use App\Models\BillingRequest;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Payout;
use App\Services\Payments\GoCardlessPaymentProcessor;
use GoCardlessPro\Resources\Payment as GoCardlessPayment;
use Illuminate\Database\Eloquent\Relations\Relation;

/**
 * Process the GoCardless payload from the webhook
 * and update the payment record in the database.
 */
class ProcessGoCardlessPaymentFromWebhook
{
    public function __construct(
        private readonly GoCardlessPaymentProcessor $goCardless,
        private readonly CreateXeroInvoicePayment $createXeroInvoicePayment,
        private readonly DeleteXeroInvoicePayment $deleteXeroInvoicePayment,
        private readonly CreateXeroBankTransactionForPayment $createXeroBankTransactionForPayment,
        private readonly DeleteXeroBankTransactionForPayment $deleteXeroBankTransactionForPayment,
    ) {}

    public function execute(string $paymentId, ?Payout $payout = null): Payment
    {
        $gcPayment = $this->goCardless->getClient()->payments()->get($paymentId);

        $payment = Payment::updateOrCreate([
            'processor_payment_id' => $gcPayment->id,
        ], [
            'status' => $gcPayment->status,
            'charge_date' => $gcPayment->charge_date,
            'amount' => $gcPayment->amount / 100,
        ]);

        // Match to the payout
        $payment->payout_id = $payment->payout_id ?: $payout?->id;
        if (! $payment->payout_id && isset($gcPayment->links->payout)) {
            $payment->payout_id = Payout::where('processor_payout_id', $gcPayment->links->payout)->first()?->id;
        }

        $payment->save();

        $this->createOrUpdateBillingRequest($gcPayment, $payment);

        if ($payment->payable_type === Relation::getMorphAlias(Invoice::class)) {
            $this->handleInvoicePaymentInXero($payment, $gcPayment);
        } else {
            $this->handleBankTransferInXero($payment, $gcPayment);
        }

        return $payment;
    }

    private function createOrUpdateBillingRequest(GoCardlessPayment $gcPayment, Payment $payment): void
    {
        $mandate = $this->goCardless->getMandate($gcPayment->links->mandate);

        $billingRequest = BillingRequest::updateOrCreate([
            'mandate_id' => $mandate->paymentProcessorMandateId,
        ], [
            'provider_customer_id' => $mandate->paymentProcessorCustomerId,
            'status' => $mandate->status,
            'mandate_activated_at' => $mandate->activatedAt,
            'mandate_url' => null,
            'expires_at' => null,
        ]);

        $payment->billing_request_id = $billingRequest->id;
        $payment->save();
    }

    private function handleInvoicePaymentInXero(Payment $payment, GoCardlessPayment $gcPayment)
    {
        if ($payment->isPaid() && $payment->payable->accounting_software_payment_id === null) {
            $this->createXeroInvoicePayment->onQueue()->execute($payment->payable);
        }
        if ($payment->isUnpaid() && $payment->payable->accounting_software_payment_id) {
            $this->deleteXeroInvoicePayment->onQueue()->execute($payment->payable);
        }
    }

    private function handleBankTransferInXero(Payment $payment, GoCardlessPayment $gcPayment)
    {
        if ($payment->isPaid() && $payment->accounting_software_bank_transfer_id === null) {
            $this->createXeroBankTransactionForPayment->onQueue()->execute($payment);
        }
        if ($payment->isUnpaid() && $payment->accounting_software_bank_transfer_id) {
            $this->deleteXeroBankTransactionForPayment->onQueue()->execute($payment);
        }
    }
}
