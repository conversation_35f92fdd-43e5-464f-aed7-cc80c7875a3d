<?php

namespace App\Actions;

use App\Models\Sale;
use App\Services\VehicleLookupService\VehicleLookupService;

class RecheckVehicleData
{
    public function __construct(private readonly VehicleLookupService $vehicleLookupService) {}

    public function execute(Sale $sale): void
    {
        try {
            $vehicle = $this->vehicleLookupService->lookup($sale->private_plate ?: $sale->vrm, usingCache: app()->isLocal());
        } catch (\Exception $e) {
            report($e);

            return;
        }

        if ($vehicle->vin !== $sale->vin) {
            throw new \RuntimeException('Vehicle VIN does not match. Different vehicle.');
        }

        $sale->update([
            'vehicle_type' => $vehicle->type,
        ]);

        $sale->ownerChanges()->updateOrCreate([
            'start_of_ownership' => $vehicle->ownerStartDate,
            'previous_owners' => $vehicle->previousOwners,
        ], [
            'last_checked_at' => now(),
        ]);
    }
}
