<?php

namespace App\Actions;

use App\Models\BillingRequest;
use App\Models\Concerns\DirectDebitableContract;
use App\Services\Payments\PaymentProcessor;

readonly class SetupBillingRequest
{
    public function __construct(
        private PaymentProcessor $paymentProcessor,
    ) {}

    public function execute(DirectDebitableContract $directDebitable, $sendNotification = false): void
    {
        if (! $directDebitable->requiresMandateSetup()) {
            throw new \RuntimeException('Direct Debit is not required for this entity.');
        }

        /** @var BillingRequest $billingRequest */
        $billingRequest = $directDebitable->billingRequest()->first();

        if (! $billingRequest || $billingRequest->expiresSoon()) {
            $billingRequestData = $this->paymentProcessor->setupBillingRequestForCustomer($directDebitable);

            $directDebitable->billingRequest()->associate(
                BillingRequest::create([
                    'provider' => $this->paymentProcessor->getProcessorIdentifier(),
                    'provider_customer_id' => $billingRequestData->paymentProcessorCustomerId,
                    'provider_billing_request_id' => $billingRequestData->paymentProcessorBillingRequestId,
                    'mandate_url' => $billingRequestData->authorisationUrl,
                    'expires_at' => $billingRequestData->urlExpiresAt,
                    'status' => $billingRequestData->status,
                    'mandate_activated_at' => null,
                ])
            )->save();
        }

        if ($sendNotification) {
            $directDebitable->sendMandateSetupNotification();
        }
    }
}
