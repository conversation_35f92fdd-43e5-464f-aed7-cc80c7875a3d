<?php

namespace App\Actions;

use App\DataTransferObjects\ClaimValidationResult;
use App\Models\Claim;

class ValidateClaim
{
    public function __construct(
        private readonly FetchMotHistory $fetchMotHistory,
        private readonly RecheckVehicleData $recheckVehicleData,
    ) {}

    public function execute(Claim $claim): ClaimValidationResult
    {
        $sale = $claim->warranty->sale;

        // Fetch latest data
        $this->fetchMotHistory->execute($sale);
        if ($sale->ownerChanges()->where('last_checked_at', '>', $claim->created_at)->doesntExist()) {
            $this->recheckVehicleData->execute($sale);
        }

        $lastOwnerChange = $sale->ownerChanges()
            ->latest('start_of_ownership')
            ->first()
            ?->start_of_ownership;

        $latestMot = $sale->motTests()
            ->valid()
            ->where('completed_at', '<', $claim->failure_date)
            ->latest('expiry_date')
            ->first();

        return new ClaimValidationResult(
            validMileage: $claim->current_mileage < $claim->warranty->mileage_cutoff,
            validMot: (bool) $latestMot,
            validOwnership: (bool) $lastOwnerChange?->lte($sale->start_date),
            needsServiceProof: $this->checkNeedsServiceProof($sale, $claim),
            customerAcceptedTerms: (bool) $claim->terms_accepted_at,
            lastOwnerChangeDate: $lastOwnerChange?->toDateString(),
            motExpiryDate: $latestMot?->expiry_date?->toDateString(),
        );
    }

    private function checkNeedsServiceProof($sale, $claim): bool
    {
        if (! $sale->last_service_date || $sale->last_service_mileage === null) {
            return true;
        }

        if ($sale->last_service_date->addMonths(12)->isBefore($claim->failure_date)) {
            return true;
        }

        if (! $sale->last_service_mileage + 12000 < $claim->failure_mileage) {
            return true;
        }

        return false;
    }
}
