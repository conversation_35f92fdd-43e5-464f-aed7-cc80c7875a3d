<?php

namespace App\Actions;

use App\Enums\PaymentProvider;
use App\Models\BillingRequest;
use App\Models\Payment;
use App\Services\Payments\PaymentException;
use App\Services\Payments\PaymentProcessor;
use App\Services\WorkingDays\WorkingDaysService;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Illuminate\Support\Facades\Log;
use Spatie\QueueableAction\QueueableAction;

class CreatePaymentInProcessor
{
    use QueueableAction;

    private BillingRequest $billingRequest;

    private Carbon|CarbonImmutable $startDate;

    private bool $isFirstPayment;

    private bool $isDraft;

    public function __construct(
        private readonly PaymentProcessor $paymentProcessor,
        private readonly WorkingDaysService $workingDaysService,
    ) {}

    public function execute(Payment $payment): void
    {
        if ($payment->provider !== PaymentProvider::ACCESS_PAYSUITE) {
            Log::warning('Payments on hold until transition to Access Paysuite', ['payment' => $payment->toArray()]);

            return;
        }

        if ($payment->processor_payment_id) {
            throw new \Exception("Payment {$payment->id} has already been processed");
        }

        $nextChargeableDate = $this->workingDaysService->addWorkingDays(3);
        if (! $payment->charge_date || $payment->charge_date->isBefore($nextChargeableDate)) {
            $payment->update([
                'charge_date' => $nextChargeableDate,
            ]);
        }

        try {
            $processorPayment = $this->paymentProcessor->createPayment($payment);

            $payment->update([
                'status' => $processorPayment->status,
                'provider' => $processorPayment->provider,
                'processor_payment_id' => $processorPayment->providerPaymentId,
                'charge_date' => $processorPayment->chargeDate,
            ]);

        } catch (PaymentException $e) {
            $payment->update([
                'status' => Payment::STATUS_FAILED,
            ]);

            $mandate = $this->paymentProcessor->getMandate($payment->billingRequest->mandate_id);

            $payment->billingRequest->update([
                'direct_debit_reference' => $mandate->directDebitReference,
                'status' => strtolower($mandate->status),
                'mandate_activated_at' => $mandate->activatedAt,
            ]);

            throw $e;
        }
    }
}
