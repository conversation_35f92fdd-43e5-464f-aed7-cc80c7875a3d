<?php

namespace App\Actions;

use App\Models\Dealership;
use App\Models\Invoice;
use App\Models\InvoiceLineItem;
use App\Models\Sale;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class GenerateMonthlyInvoiceToDealership
{
    public function execute(Dealership $dealership, Carbon $start): ?Invoice
    {
        if (! $dealership->accountingContact) {
            Log::info('Dealership has not been linked to accounting software contact yet.', [
                'dealership' => $dealership->only('id', 'name'),
            ]);

            return null;
        }

        $start = $start->clone()->startOfMonth();
        $end = $start->clone()->endOfMonth();

        $sales = $dealership->sales()
            ->with('warranty.product.coverLevel', 'breakdownPlan.product', 'servicePlan.product')
            ->active()
            ->whereDoesntHave('invoiceLineItems.invoice')
            ->whereBetween('created_at', [$start, $end])
            ->get();

        if ($sales->isEmpty()) {
            Log::info('There are no sales for the selected month.', [
                'dealership' => $dealership->only('id', 'name'),
            ]);

            return null;
        }

        /** @var Invoice $invoice */
        $invoice = $dealership->invoices()->updateOrCreate([
            'account_id' => $dealership->account_id,
            'period_start' => $start->toDateString(),
            'period_end' => $end->toDateString(),
        ], [
            'date' => $end->toDateString(),
            'due_date' => $end->clone()->addDay()->toDateString(),
            'description' => "{$dealership->name} warranty services for ".$start->format('F Y'),
        ]);

        $invoice->lineItems()->whereNotIn('sale_id', $sales->pluck('id'))->delete();

        $sales->each(function (Sale $sale) use ($invoice) {
            if ($sale->warranty) {
                $invoice->lineItems()->updateOrCreate([
                    'sale_id' => $sale->id,
                    'type' => InvoiceLineItem::TYPE_WARRANTY,
                ], [
                    'quantity' => 1,
                    'account_code' => $sale->warranty->nominalAccountCodeForAdminFee(),
                    'description' => "{$sale->id}: Warranty for {$sale->vrm}: {$sale->vehicle_details} | {$sale->warranty->product->coverLevel->name} {$sale->warranty->product->period} months",
                    'unit_amount' => $sale->warranty->admin_fee,
                    'tax' => $sale->warranty->vat,
                ]);
            }
            if ($sale->breakdownPlan) {
                $invoice->lineItems()->updateOrCreate([
                    'sale_id' => $sale->id,
                    'type' => InvoiceLineItem::TYPE_BREAKDOWN,
                ], [
                    'description' => "{$sale->id}: Breakdown Plan for {$sale->vrm}: {$sale->vehicle_details} | {$sale->breakdownPlan->product->name} {$sale->breakdownPlan->product->period} months",
                    'quantity' => 1,
                    'account_code' => $sale->breakdownPlan->nominalAccountCode(),
                    'unit_amount' => $sale->breakdownPlan->admin_fee,
                    'tax' => $sale->breakdownPlan->vat,
                ]);
            }
            if ($sale->servicePlan && ! $sale->servicePlan->isRecurring()) {
                // TODO - how do we collect admin fees for recurring service plans?
                $invoice->lineItems()->updateOrCreate([
                    'sale_id' => $sale->id,
                    'type' => InvoiceLineItem::TYPE_SERVICE_PLAN,
                ], [
                    'description' => "{$sale->id}: Service Plan for {$sale->vrm}: {$sale->vehicle_details} | {$sale->servicePlan->product->name} {$sale->servicePlan->duration_years} years",
                    'quantity' => 1,
                    'account_code' => $sale->servicePlan->nominalAccountCodeForAdminFee(),
                    'unit_amount' => $sale->servicePlan->admin_fee,
                    'tax' => $sale->servicePlan->vat,
                ]);
            }
        });

        return $invoice;
    }
}
