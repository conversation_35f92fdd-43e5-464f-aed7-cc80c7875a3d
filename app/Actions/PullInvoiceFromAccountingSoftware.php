<?php

namespace App\Actions;

use App\Models\Invoice;
use App\Services\Accounting\AccountingService;
use Spatie\QueueableAction\QueueableAction;

class PullInvoiceFromAccountingSoftware
{
    use QueueableAction;

    public function __construct(
        protected AccountingService $accountingService,
        protected UpdateDatabaseInvoiceFromValueObject $updateDatabaseInvoiceFromValueObject,
    ) {}

    public function execute(string $resourceId): Invoice
    {
        $invoiceData = $this->accountingService->getInvoice($resourceId);

        /** @var Invoice $invoice */
        $invoice = Invoice::query()
            ->withoutGlobalScope('tenant')
            ->where('accounting_software_id', $invoiceData->id)
            ->sole();

        return $this->updateDatabaseInvoiceFromValueObject->execute($invoice, $invoiceData);
    }
}
