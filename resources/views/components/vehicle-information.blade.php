@props([
    'sale'
])

<div
    {{ $attributes->class(['flex items-center gap-2 fi-ta-text-item-label text-sm leading-6 text-gray-950 dark:text-white']) }}
>
    <x-numberplate :sale="$sale"/>
    @if($sale->manufacturer)
        <img src="{{ $sale->manufacturer->logoUrl() }}" alt="{{ $sale->manufacturer->name }}"
             class="w-12"
        >
    @else
        <div class="size-12 bg-gray-200 rounded flex items-center justify-center">
            <x-lucide-car class="size-8 text-gray-500 mt-0.5 shrink-0"/>
        </div>
    @endif
    <div class="flex flex-col leading-tight">
        <div class="font-semibold">
            {{ $sale->registration_date->year }}
            {{ $sale->vehicle_make }}
        </div>
        <div>{{ $sale->vehicle_model }}</div>
        <div class="text-xs text-gray-500">{{ $sale->vehicle_derivative }}</div>
    </div>
</div>
