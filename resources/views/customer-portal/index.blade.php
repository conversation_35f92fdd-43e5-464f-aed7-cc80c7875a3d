<x-layouts.app>
    <div class="max-w-3xl mx-auto">
        <flux:heading size="xl" level="1">Hello, {{ $customer->first_name }}</flux:heading>

        <flux:subheading size="lg" class="mb-6">Here you can view and manage your policy</flux:subheading>

        <flux:separator variant="subtle"/>

        <div class="my-8 grid grid-cols-2 md:grid-cols-3 gap-4">
            <a wire:navigate href="{{ route('customer-portal.home') }}">
                <flux:card class="flex flex-col items-center">
                    <flux:icon name="document-text" class="text-accent mb-2"/>
                    <flux:heading size="lg">Policy Summary</flux:heading>
                    <flux:subheading>View your product details</flux:subheading>
                </flux:card>
            </a>
            <a wire:navigate href="{{ route('customer-portal.documents') }}">
                <flux:card class="flex flex-col items-center">
                    <flux:icon name="document" class="text-accent mb-2"/>
                    <flux:heading size="lg">Documents</flux:heading>
                    <flux:subheading>Download your documents</flux:subheading>
                </flux:card>
            </a>
            <a wire:navigate href="{{ route('customer-portal.update-details') }}">
                <flux:card class="flex flex-col items-center">
                    <flux:icon name="pencil-square" class="text-accent mb-2"/>
                    <flux:heading size="lg">Make a Change</flux:heading>
                    <flux:subheading>Change your personal details</flux:subheading>
                </flux:card>
            </a>
            <a wire:navigate href="#">
                <flux:card class="flex flex-col items-center">
                    <flux:icon name="plus" class="text-accent mb-2"/>
                    <flux:heading size="lg">Add to Policy</flux:heading>
                    <flux:subheading>Add a product to your policy</flux:subheading>
                </flux:card>
            </a>
            <a wire:navigate href="{{ route('customer-portal.payments') }}">
                <flux:card class="flex flex-col items-center">
                    <flux:icon name="credit-card" class="text-accent mb-2"/>
                    <flux:heading size="lg">Payments</flux:heading>
                    <flux:subheading>View your payment details</flux:subheading>
                </flux:card>
            </a>
            <a wire:navigate href="#">
                <flux:card class="flex flex-col items-center">
                    <flux:icon name="shield-exclamation" class="text-accent mb-2"/>
                    <flux:heading size="lg">Claims</flux:heading>
                    <flux:subheading>View or make a claim</flux:subheading>
                </flux:card>
            </a>
        </div>

        <div class="grid grid-cols-1 gap-4" x-data="{ visibleKey: null }">
            @foreach($customer->sales as $sale)
                <x-product-card :heading="'Reference: ' . $sale->id"
                                :key="$sale->id"
                                :sale="$sale"
                >
                    <x-definition-list class="bg-white -mx-6 -mb-6 pb-6 px-6">
                        <x-definition-list.group heading="Policy Overview">
                            <x-definition-list.item label="Reference" :value="$sale->id"/>
                            <x-definition-list.item label="Start Date" :value="$sale->start_date->format('d F Y')"/>
                        </x-definition-list.group>
                        <x-definition-list.group heading="Vehicle">
                            <x-definition-list.item label="Registration" :value="$sale->vrm"/>
                            <x-definition-list.item label="Vehicle" :value="$sale->vehicle_details"/>
                            <x-definition-list.item label="Registered Date"
                                                    :value="$sale->registration_date->format('d/m/Y')"
                            />
                            <x-definition-list.item label="Fuel Type" :value="$sale->fuel_type"/>
                            <x-definition-list.item label="Transmission" :value="$sale->transmission_type"/>
                            <x-definition-list.item label="Mileage at start of policy"
                                                    :value="number_format($sale->delivery_mileage)"
                            />
                        </x-definition-list.group>
                        @if($sale->warranty)
                            <x-definition-list.group heading="Warranty Details">
                                <x-definition-list.item label="Renewal Date"
                                                        :value="$sale->warranty->end_date ? $sale->warranty->end_date->format('d F Y') : 'Monthly Subscription'"
                                />
                                <x-definition-list.item label="Annual Mileage Limit"
                                                        :value="$sale->warranty->annual_mileage_limit ? number_format($sale->warranty->annual_mileage_limit) : null"
                                />
                                <x-definition-list.item label="Maximum Hourly Labour Rate"
                                                        :value="'£' . number_format($sale->account->max_hourly_labour_rate, 2)"
                                />
                                <x-definition-list.item label="Level of Cover"
                                                        :value="$sale->warranty->product->coverLevel->name"
                                />
                                <x-definition-list.item label="Term"
                                                        :value="$sale->warranty->product->period . ' ' . \Illuminate\Support\Str::plural('month', $sale->warranty->product->period)"
                                />
                            </x-definition-list.group>
                        @endif
                        @if($sale->breakdownPlan)
                            <x-definition-list.group heading="Roadside Recovery Details">
                                <x-definition-list.item label="Renewal Date"
                                                        :value="$sale->breakdownPlan->end_date ? $sale->breakdownPlan->end_date->format('d F Y') : 'Monthly Subscription'"
                                />
                                <x-definition-list.item label="Product" :value="$sale->breakdownPlan->product->name"/>
                                <x-definition-list.item label="Term"
                                                        :value="$sale->breakdownPlan->product->period . ' ' . \Illuminate\Support\Str::plural('month', $sale->breakdownPlan->product->period)"
                                />
                            </x-definition-list.group>
                        @endif
                        @if($sale->servicePlan)
                            <x-definition-list.group heading="Service Plan Details">
                                <x-definition-list.item label="Renewal Date"
                                                        :value="$sale->servicePlan->end_date ? $sale->servicePlan->end_date->format('d F Y') : 'Monthly Subscription'"
                                />
                                <x-definition-list.item label="Product" :value="$sale->servicePlan->product->name"/>
                                <x-definition-list.item label="Term"
                                                        :value="$sale->servicePlan->duration_years . ' years'"
                                />
                            </x-definition-list.group>
                        @endif
                    </x-definition-list>
                </x-product-card>
            @endforeach
        </div>
    </div>
</x-layouts.app>
