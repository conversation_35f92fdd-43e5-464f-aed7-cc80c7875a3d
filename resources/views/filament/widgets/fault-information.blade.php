<x-filament-widgets::widget>
    <x-filament::section heading="Fault Information">
        <div class="flex items-start gap-3">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                 stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                 class="lucide lucide-wrench h-5 w-5 text-gray-500 mt-0.5 shrink-0"
            >
                <path
                    d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"
                ></path>
            </svg>
            <div>
                <h3 class="font-bold">Fault: {{ $claim->faultType->name }}</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400"
                >
                    {{ $claim->fault_description }}
                </p>
                <p class="text-sm text-gray-600 dark:text-gray-400"
                >Date of Failure: <span class="font-medium">{{ $claim->failure_date->formatLocal() }}</span></p>
                <p class="text-sm text-gray-600 dark:text-gray-400">Reported Mileage: <span class="font-medium">
                    {{ number_format($claim->current_mileage) }}
                </span>
                </p>
            </div>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
