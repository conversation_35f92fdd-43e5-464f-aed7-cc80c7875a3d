<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot:heading>
            <div class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="lucide lucide-square-check-big h-5 w-5 text-primary"
                >
                    <path d="M21 10.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.5"></path>
                    <path d="m9 11 3 3L22 4"></path>
                </svg>
                Claim Validation Checklist
            </div>
        </x-slot:heading>
        <div class="flex flex-col gap-3">
            <div class="flex items-center gap-3 text-sm">
                @if($this->validationResult->validMileage)
                    <x-filament::icon icon="heroicon-o-check-circle" class="text-green-500 size-5 flex-shrink-0"/>
                    Mileage is within limits
                @else
                    <x-filament::icon icon="heroicon-o-x-circle" class="text-red-500 size-5 flex-shrink-0"/>
                    Mileage is {{ number_format($claim->current_mileage - $claim->warranty->mileage_cutoff) }} over limit
                @endif
            </div>
            <div class="flex items-center gap-3 text-sm">
                @if($this->validationResult->validOwnership)
                    <x-filament::icon icon="heroicon-o-check-circle" class="text-green-500 size-5 flex-shrink-0"/>
                    No reported DVLA keeper changes
                @else
                    <x-filament::icon icon="heroicon-o-x-circle" class="text-red-500 size-5 flex-shrink-0"/>
                    DVLA records a keeper change at {{ Carbon\Carbon::parse($this->validationResult->lastOwnerChangeDate)->formatLocal() }}
                @endif
            </div>
            <div class="flex items-center gap-3 text-sm">
                @if($this->validationResult->validMot)
                    <x-filament::icon icon="heroicon-o-check-circle" class="text-green-500 size-5 flex-shrink-0"/>
                    MOT is valid
                @else
                    <x-filament::icon icon="heroicon-o-x-circle" class="text-red-500 size-5 flex-shrink-0"/>
                    @if($this->validationResult->motExpiryDate)
                        MOT expired on {{ $this->validationResult->motExpiryDate }}
                    @else
                        No MOT tests found
                    @endif
                @endif
            </div>
            <div class="flex items-center gap-3 text-sm">
                @if($this->validationResult->needsServiceProof)
                    <x-filament::icon icon="heroicon-o-exclamation-circle" class="text-amber-500 size-5 flex-shrink-0"/>
                    Needs proof of servicing
                @else
                    <x-filament::icon icon="heroicon-o-x-circle" class="text-green-500 size-5 flex-shrink-0"/>
                    Servicing proof not required (completed prior to sale)
                @endif
            </div>
            <div class="flex items-center gap-3 text-sm">
                @if($this->validationResult->customerAcceptedTerms)
                    <x-filament::icon icon="heroicon-o-check-circle" class="text-green-500 size-5 flex-shrink-0"/>
                    Customer has accepted terms
                @else
                    <x-filament::icon icon="heroicon-o-x-circle" class="text-red-500 size-5 flex-shrink-0"/>
                    Customer has not accepted terms
                @endif
            </div>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
