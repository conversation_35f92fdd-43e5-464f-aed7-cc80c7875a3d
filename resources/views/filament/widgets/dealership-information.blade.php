<x-filament-widgets::widget>
    <x-filament::section heading="Vehicle Information">
        <div class="flex items-start gap-3">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                 stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                 class="lucide lucide-car h-5 w-5 text-gray-500 mt-0.5 shrink-0"
            >
                <path
                    d="M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2"
                ></path>
                <circle cx="7" cy="17" r="2"></circle>
                <path d="M9 17h6"></path>
                <circle cx="17" cy="17" r="2"></circle>
            </svg>
            <div>
                <a href="{{ \App\Filament\Resources\SaleResource::getUrl('view', [$sale]) }}" target="_blank">
                    <h3 class="font-bold">{{ $sale->vehicle_details }}</h3>
                </a>
                <p class="text-sm text-gray-600 dark:text-gray-400"
                >First registered: <span class="font-medium">{{  $sale->registration_date->formatLocal() }}</span></p>
                <p class="text-sm text-gray-600 dark:text-gray-400"
                >Registration: <span class="font-medium">{{ $sale->vrm }}</span></p>
                <p class="text-sm text-gray-600 dark:text-gray-400">Delivery Mileage: <span class="font-medium">
                    {{ number_format($sale->delivery_mileage) }}
                </span>
                </p>
                <p class="text-sm text-gray-600 dark:text-gray-400"
                >Sold by: <span class="font-medium">{{ $sale->dealership->name }}</span></p>
            </div>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
