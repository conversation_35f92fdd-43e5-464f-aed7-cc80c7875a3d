<div class="flex items-start gap-3">
    <div class="flex flex-col gap-2">
        <a href="{{ \App\Filament\Resources\SaleResource::getUrl('view', [$sale]) }}" target="_blank">
        <x-vehicle-information :sale="$sale"/>
        </a>
        <div class="flex flex-col gap-1">
        <p class="text-sm text-gray-600 dark:text-gray-400"
        >First registered: <span class="font-medium">{{  $sale->registration_date->formatLocal() }}</span></p>
        <p class="text-sm text-gray-600 dark:text-gray-400"
        >Delivery Date: <span class="font-medium">{{ $sale->start_date->formatLocal() }}</span></p>
        <p class="text-sm text-gray-600 dark:text-gray-400">Delivery Mileage: <span class="font-medium">
                {{ number_format($sale->delivery_mileage) }}
            </span>
        </p>
        @if($sale->last_service_mileage || $sale->last_service_date)
            <p class="text-sm text-gray-600 dark:text-gray-400">Last Service
                @if($sale->last_service_mileage)<span class="font-medium">
                        at {{ number_format($sale->last_service_mileage) }}
                    @endif
                    @if($sale->last_service_date)
                        on {{ $sale->last_service_date->formatLocal() }}
                    @endif
                </span>
            </p>
        @endif
    </div>
    </div>
</div>
