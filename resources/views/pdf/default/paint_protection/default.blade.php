<div class="print-page">
    <div class="m-8 border-4 rounded-lg p-8">
        <div class="grid grid-cols-3 gap-x-4 items-center">
            <img src="{{ asset('assets/duracare.png') }}"
                 class="w-72 h-auto"
                 alt="Logo URL"
            />
            <div class="text-center">
                <h1 class="font-bold text-3xl">{{ $accountProduct->product->name }} Total Vehicle Protection</h1>

                <h2 class="mt-5 text-cyan-500 font-semibold uppercase text-xl tracking-widest">Warranty
                    Registration</h2>
            </div>

            <div class="text-right">
                {{ $sale->dealership->name }}<br/>
                {{ $sale->dealership->fullAddress() }}
            </div>
        </div>

        <div class="mt-4 border-2 rounded-lg p-4">
            <h2 class="text-xl font-semibold text-gray-500">Customer Details</h2>
            <div class="mt-5">
                <div class="grid grid-cols-2 gap-x-4">
                    <dl class="border-t border-gray-200">
                        @include('pdf._key_value_wide', ['label' => 'Full name', 'value' => $sale->customer->full_name])
                        @include('pdf._key_value_wide', ['label' => 'Email', 'value' => $sale->customer->email])
                        @include('pdf._key_value_wide', ['label' => 'Phone', 'value' => $sale->customer->phone])
                    </dl>
                    <dl class="border-t border-gray-200">
                        @include('pdf._key_value_wide', ['label' => 'Address', 'value' => $sale->customer->fullAddress(), 'fullHeight' => true])
                    </dl>
                </div>
            </div>
        </div>

        <div class="mt-4 border-2 rounded-lg p-4">
            <h2 class="text-xl font-semibold text-gray-500">Vehicle Details</h2>
            <div class="mt-5">
                <div class="grid grid-cols-2 gap-x-4">
                    <dl class="border-t border-gray-200">
                        @include('pdf._key_value_wide', ['label' => 'Registration', 'value' => $sale->vrm])
                        @includeWhen($sale->private_plate, 'pdf._key_value_wide', ['label' => 'Private Plate', 'value' => $sale->private_plate])
                        @include('pdf._key_value_wide', ['label' => 'Manufacturer', 'value' => $sale->vehicle_make])
                        @include('pdf._key_value_wide', ['label' => 'Model', 'value' => $sale->vehicle_model])
                        @include('pdf._key_value_wide', ['label' => 'Derivative', 'value' => $sale->vehicle_derivative])
                        @include('pdf._key_value_wide', ['label' => 'Engine Capacity', 'value' => $sale->engine_capacity . 'cc'])
                        @include('pdf._key_value_wide', ['label' => 'Colour', 'value' => $sale->vehicle_colour])

                    </dl>
                    <dl class="border-t border-gray-200">
                        @include('pdf._key_value_wide', ['label' => 'Date of Registration', 'value' => $sale->registration_date->format('d/m/Y')])
                        @include('pdf._key_value_wide', ['label' => 'Fuel Type', 'value' => $sale->fuel_type])
                        @include('pdf._key_value_wide', ['label' => 'Transmission Type', 'value' => $sale->transmission_type])
                        @include('pdf._key_value_wide', ['label' => 'Delivery Mileage', 'value' => number_format($sale->delivery_mileage)])
                        @include('pdf._key_value_wide', ['label' => 'Vehicle Price', 'value' => '£' . number_format($sale->vehicle_price_paid)])
                        @include('pdf._key_value_wide', ['label' => 'VIN Number', 'value' => $sale->vin])
                    </dl>
                </div>
            </div>
        </div>

        <div class="mt-4 border-2 rounded-lg p-4">
            <h2 class="text-xl font-semibold text-gray-500">Paint Warranty Details</h2>
            <div class="mt-5">
                <div class="grid grid-cols-2 gap-x-4">
                    <dl class="border-t border-gray-200">
                        @include('pdf._key_value_wide', ['label' => 'Agreement Number', 'value' => $sale->id])
                        @include('pdf._key_value_wide', ['label' => 'Purchase Date', 'value' => $sale->created_at->format('d/m/Y')])
                        @include('pdf._key_value_wide', [
                            'label' => 'Selling Price',
                            'value' => '£' . number_format($accountProduct->selling_price, 2)
                        ])
                    </dl>
                    <dl class="border-t border-gray-200">
                        @include('pdf._key_value_wide', ['label' => 'Product', 'value' => $accountProduct->product->name])
                        @include('pdf._key_value_wide', ['label' => 'Start Date', 'value' => $sale->start_date->format('d/m/Y')])
                        @include('pdf._key_value_wide', ['label' => 'Start Mileage', 'value' => number_format($sale->delivery_mileage) . ' miles'])
                        @includeWhen($sale->salesPerson, 'pdf._key_value_wide', ['label' => 'Sales Person', 'value' => $sale->salesPerson?->name])
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>
@include('pdf.default.paint_protection._warranty')
