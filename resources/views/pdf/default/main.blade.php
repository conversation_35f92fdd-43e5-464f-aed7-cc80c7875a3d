<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    >
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Sale #{{ $sale->id }}</title>
    <link rel="stylesheet" href="{{ Storage::disk('public')->url('assets/app.css') }}?v={{ filemtime(public_path('build/manifest.json')) }}">
</head>
<body class="font-display">

@if($sale->warranty)
    @include('pdf.default._welcome', ['warranty' => $sale->warranty])

    @includeWhen($qrCode, 'pdf.default._qr_code')

    @include('pdf.default._validation_certificate', ['warranty' => $sale->warranty])

    @include('pdf.default._definitions', ['warranty' => $sale->warranty])

    @foreach($documents as $document)
        <div class="print-page prose max-w-max">
            <div class="m-8 border-4 rounded-lg p-8 space-y-5">
                {!! str($document)->markdown()->sanitizeHtml() !!}
            </div>
        </div>
    @endforeach

    @include('pdf.default._how_to_claim')

    @include('pdf.default._terms_and_conditions')

    @include('pdf.default._general_exclusions')
@endif

@if($sale->breakdownPlan)
    @includeIf('pdf.default.breakdown_cover.' . str($sale->breakdownPlan->product->name)->snake())
@endif

@if($sale->servicePlan)
    @includeIf('pdf.default.service_plan.' . str($sale->servicePlan->product->name)->snake())
@endif

@foreach($sale->products as $accountProduct)
    @if(view()->exists('pdf.default.' . str($accountProduct->product->group->name)->snake() . '.' . str($accountProduct->product->name)->snake()))
        @include('pdf.default.' . str($accountProduct->product->group->name)->snake() . '.' . str($accountProduct->product->name)->snake())
    @else
        @includeIf('pdf.default.' . str($accountProduct->product->group->name)->snake() . '.default')
    @endif
@endforeach

</body>
</html>
