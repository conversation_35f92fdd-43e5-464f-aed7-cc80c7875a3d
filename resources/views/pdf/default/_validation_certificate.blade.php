<div class="print-page">
    <div class="m-8 border-4 rounded-lg p-8">
        <div class="grid grid-cols-2 gap-x-4">
            <div>
                <h1 class="font-bold text-3xl">
                    {{ $warranty->product->coverLevel->name }} Warranty
                    -
                    @if($warranty->product->is_recurring)
                        Monthly
                    @else
                        {{ $warranty->product->period }} {{ Str::plural('month', $warranty->product->period) }}
                    @endif
                </h1>

                <h2 class="mt-5 font-bold text-2xl">Validation Certificate</h2>
            </div>

            <div class="text-right space-y-4">
                @if($warranty->account->logoUrl())
                    <img src="{{ $warranty->account->logoUrl() }}"
                         class="ml-auto w-72 h-auto"
                         alt="Logo URL"
                    >
                @endif
                <div>
                    {{ $warranty->sale->dealership->name }}<br/>
                    {{ $warranty->sale->dealership->fullAddress() }}
                </div>
            </div>
        </div>

        <div class="mt-4 border-2 rounded-lg p-4">
            <h2 class="text-xl font-semibold text-gray-500">Customer Details</h2>
            <div class="mt-5">
                <div class="grid grid-cols-2 gap-x-4">
                    <dl class="border-t border-gray-200">
                        @include('pdf._key_value_wide', ['label' => 'Full name', 'value' => $warranty->sale->customer->full_name])
                        @include('pdf._key_value_wide', ['label' => 'Email', 'value' => $warranty->sale->customer->email])
                        @include('pdf._key_value_wide', ['label' => 'Phone', 'value' => $warranty->sale->customer->phone])
                    </dl>
                    <dl class="border-t border-gray-200">
                        @include('pdf._key_value_wide', ['label' => 'Address', 'value' => $warranty->sale->customer->fullAddress()])
                    </dl>
                </div>
            </div>
        </div>

        <div class="mt-4 border-2 rounded-lg p-4">
            <h2 class="text-xl font-semibold text-gray-500">Vehicle Details</h2>
            <div class="mt-5">
                <div class="grid grid-cols-2 gap-x-4">
                    <dl class="border-t border-gray-200">
                        @include('pdf._key_value_wide', ['label' => 'Registration', 'value' => $warranty->sale->vrm])
                        @includeWhen($warranty->sale->private_plate, 'pdf._key_value_wide', ['label' => 'Private Plate', 'value' => $warranty->sale->private_plate])
                        @include('pdf._key_value_wide', ['label' => 'Manufacturer', 'value' => $warranty->sale->vehicle_make])
                        @include('pdf._key_value_wide', ['label' => 'Model', 'value' => $warranty->sale->vehicle_model])
                        @include('pdf._key_value_wide', ['label' => 'Derivative', 'value' => $warranty->sale->vehicle_derivative])
                        @include('pdf._key_value_wide', ['label' => 'Engine Capacity', 'value' => $warranty->sale->engine_capacity . 'cc'])
                        @include('pdf._key_value_wide', ['label' => 'Colour', 'value' => $warranty->sale->vehicle_colour])
                        @include('pdf._key_value_wide', ['label' => 'Date of Registration', 'value' => $warranty->sale->registration_date->format('d/m/Y')])

                    </dl>
                    <dl class="border-t border-gray-200">
                        @include('pdf._key_value_wide', ['label' => 'Fuel Type', 'value' => $warranty->sale->fuel_type])
                        @include('pdf._key_value_wide', ['label' => 'Transmission Type', 'value' => $warranty->sale->transmission_type])
                        @include('pdf._key_value_wide', ['label' => 'Delivery Mileage', 'value' => number_format($warranty->sale->delivery_mileage)])
                        @include('pdf._key_value_wide', ['label' => 'Vehicle Price', 'value' => '£' . number_format($warranty->sale->vehicle_price_paid)])
                        @include('pdf._key_value_wide', ['label' => 'VIN Number', 'value' => $warranty->sale->vin])
                        @includeWhen($warranty->last_service_date, 'pdf._key_value_wide', ['label' => 'Last Service Date', 'value' => $warranty->last_service_date?->format('d/m/Y')])
                        @includeWhen($warranty->last_service_mileage, 'pdf._key_value_wide', ['label' => 'Last Service Mileage', 'value' => number_format($warranty->last_service_mileage)])
                    </dl>
                </div>
            </div>
        </div>

        <div class="mt-4 border-2 rounded-lg p-4">
            <h2 class="text-xl font-semibold text-gray-500">Warranty Details</h2>
            <div class="mt-5">
                <div class="grid grid-cols-2 gap-x-4">
                    <dl class="border-t border-gray-200">
                        @include('pdf._key_value_wide', ['label' => 'Agreement Number', 'value' => $warranty->id])
                        @include('pdf._key_value_wide', ['label' => 'Purchase Date', 'value' => $warranty->created_at->format('d/m/Y')])
                        @include('pdf._key_value_wide', ['label' => 'Start Date', 'value' => $warranty->sale->start_date->format('d/m/Y')])
                        @includeWhen(!$warranty->isRecurring(), 'pdf._key_value_wide', ['label' => 'End Date', 'value' => $warranty->end_date?->format('d/m/Y')])
                        @include('pdf._key_value_wide', ['label' => 'Start Mileage', 'value' => number_format($warranty->sale->delivery_mileage) . ' miles'])
                        @includeWhen($warranty->annual_mileage_limit, 'pdf._key_value_wide', ['label' => 'Annual Mileage Limit', 'value' => number_format($warranty->annual_mileage_limit) . ' miles'])
                    </dl>
                    <dl class="border-t border-gray-200">
                        @include('pdf._key_value_wide', ['label' => 'Cover Level', 'value' => $warranty->product->coverLevel->name])
                        @include('pdf._key_value_wide', ['label' => 'Individual Claim Limit', 'value' => '£' . number_format($warranty->individual_claim_limit) . ' (inc VAT)'])
                        @include('pdf._key_value_wide', ['label' => 'Total Claim Limit', 'value' => '£' . number_format($warranty->total_claim_limit) . ' (inc VAT)'])
                        @include('pdf._key_value_wide', ['label' => 'Maximum Labour Hourly Rate', 'value' => '£' . number_format($warranty->account->max_hourly_labour_rate) . ' (plus VAT)'])
                        @include('pdf._key_value_wide', [
                            'label' => 'Warranty Sale Price',
                            'value' => '£' . number_format($warranty->selling_price, 2) . ($warranty->isRecurring() ? ' then £' . number_format($warranty->monthly_selling_price, 2) . ' monthly' : '')
                        ])
                        @includeWhen($warranty->annual_mileage_limit && !$warranty->isRecurring(), 'pdf._key_value_wide', ['label' => 'Mileage Cutoff', 'value' => number_format($warranty->mileage_cutoff) . ' miles'])
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>
