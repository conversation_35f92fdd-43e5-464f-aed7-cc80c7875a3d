<div>
    <div class="flex gap-4 items-start justify-between">
        <div class="flex items-start gap-2.5 mb-2">
            <flux:icon name="hand-helping" class="text-accent mb-2"/>
            <flux:heading size="lg">My Offers</flux:heading>
        </div>
    </div>
    <div class="flex flex-col gap-4">
        @foreach($this->offers as $offer)
            <flux:card class="bg-gray-50 overflow-hidden">
                <div class="flex items-end justify-between">
                    <div>
                        <flux:heading accent size="xl" level="2" class="mb-2">{{ $offer->salesLead->sale->formattedVrm() }}</flux:heading>
{{--                        <flux:heading size="lg">{{ $offer->salesLead->sale->vrm }}</flux:heading>--}}
                        <flux:subheading>{{ $offer->salesLead->sale->vehicle_details }} {{ $offer->salesLead->sale->vehicle_derivative }}</flux:subheading>
                        <p>
                            Warranty: {{ $offer->warrantyProduct->getLabel() }}
                        </p>
                    </div>
                    <div class="flex flex-col gap-2">
                        <flux:button variant="primary"
                                     :href="route('customer-portal.offers.view', $offer)"
                                     size="sm"
                        >View offer
                            <flux:icon name="chevron-right" variant="micro"/>
                        </flux:button>
                    </div>
                </div>

            </flux:card>
        @endforeach
    </div>
</div>
