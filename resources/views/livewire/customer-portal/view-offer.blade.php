<div>
    <div class="flex gap-4 items-start justify-between">
        <div class="flex items-start gap-2.5 mb-2">
            <flux:icon name="hand-helping" class="text-accent mb-2"/>
            <flux:heading size="lg">My Personalised Offer</flux:heading>
        </div>
    </div>
    <div class="flex flex-col gap-4">
        <flux:card class="bg-gray-50 overflow-hidden">
            <div class="flex items-end justify-between">
                <div>
                    <flux:heading accent size="xl" level="2" class="mb-2"
                    >{{ $offer->salesLead->sale->formattedVrm() }}</flux:heading>
                    {{--                        <flux:heading size="lg">{{ $offer->salesLead->sale->vrm }}</flux:heading>--}}
                    <flux:subheading>{{ $offer->salesLead->sale->vehicle_details }} {{ $offer->salesLead->sale->vehicle_derivative }}</flux:subheading>
                    <p>
                        Warranty: {{ $offer->warrantyProduct->getLabel() }}
                    </p>
                </div>
                <div class="flex flex-col gap-2">
                    <flux:button
                        href="#"
                        size="sm"
                    >Pay in 10 monthly instalments of £{{ number_format($offer->warranty_selling_price / 10, 2) }}
                        <flux:icon name="chevron-right" variant="micro"/>
                    </flux:button>

                    <flux:button variant="primary"
                                 href="#"
                                 size="sm"
                    >Pay by card £{{ number_format($offer->warranty_selling_price, 2) }}
                        <flux:icon name="chevron-right" variant="micro"/>
                    </flux:button>
                </div>
            </div>

        </flux:card>
    </div>
</div>
