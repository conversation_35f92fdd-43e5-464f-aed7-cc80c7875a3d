<div>
    <form wire:submit.prevent="save">
        <div class="space-y-6">
            <flux:card class="max-w-4xl mx-auto bg-red-100 space-y-6">
                <flux:select
                    wire:model="sale_id"
                    variant="listbox"
                    label="Select Vehicle"
                    placeholder="Choose a vehicle..."
                >
                    @foreach($this->validSales as $sale)
                        <flux:option value="{{ $sale->id }}">{{ $sale->vehicle_details }} ({{ $sale->vrm }})
                        </flux:option>
                    @endforeach
                </flux:select>
                <flux:checkbox.group wire:model.live="checklist" label="Claims Checklist">
                    @foreach($this->checklistItems as $key => $label)
                        <flux:checkbox label="{{ $label }}" value="{{ $key }}"/>
                    @endforeach
                </flux:checkbox.group>
            </flux:card>


            <fieldset
                @if(count($this->checklist) < count($this->checklistItems)) disabled @endif
            >

                <flux:card class="max-w-4xl mx-auto space-y-6">
                    <div class="grid md:grid-cols-2 gap-6">


                        <flux:input type="date" wire:model="failure_date" :max="today()->toDateString()"
                                    label="Date of Failure"
                        />
                        <flux:input type="number" wire:model="current_mileage" label="Current Mileage"/>
                        <flux:select
                            wire:model="fault_type_id"
                            variant="listbox"
                            label="Type of Fault"
                            placeholder="Choose type of fault..."
                        >
                            @foreach($this->faultTypes as $key => $label)
                                <flux:option value="{{ $key }}">{{ $label }}</flux:option>
                            @endforeach
                        </flux:select>
                    </div>
                    <div class="grid md:grid-cols-2 gap-6 ">
                        <flux:textarea
                            wire:model="fault_description"
                            label="Description of Fault"
                            placeholder="Please provide a detailed description of the symptoms or fault with the vehicle."
                            resize="none"
                        />
                        <flux:textarea
                            wire:model="vehicle_location"
                            label="Vehicle Location"
                            placeholder="Where is the vehicle now?"
                            resize="none"
                        />
                    </div>
                    <div class="flex justify-end">
                        <flux:button variant="primary" type="submit">Submit Claim</flux:button>
                    </div>
                </flux:card>
            </fieldset>
        </div>
    </form>
</div>
