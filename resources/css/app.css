@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
        --color-accent: var(--color-red-500);
        --color-accent-content: var(--color-red-600);
        --color-accent-foreground: var(--color-white);
    }

    .dark {
        --color-accent: var(--color-red-500);
        --color-accent-content: var(--color-red-400);
        --color-accent-foreground: var(--color-white);
    }

    @page {
        size: A4;
        margin: 0;
    }

    .print-page {
        min-height: 29.7cm;
        margin: 1cm auto;
        background: white;
    }

    @media print {
        .print-page {
            margin: 0;
            border: initial;
            border-radius: initial;
            width: initial;
            min-height: initial;
            box-shadow: initial;
            background: initial;
            page-break-after: always;
        }
    }
}

