<?php

namespace OvonGroup\ManufacturerLogos\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use OvonGroup\ManufacturerLogos\Models\Manufacturer;

class SeedManufacturerLogosCommand extends Command
{
    public $signature = 'manufacturer-logos:seed';

    public $description = 'Seed manufacturer logos';

    public function handle(): int
    {
        foreach ($this->getManufacturers() as $manufacturer) {
            $name = match ($manufacturer['name']) {
                'Mercedes Benz' => 'Mercedes',
                default => $manufacturer['name'],
            };

            $dbManufacturer = Manufacturer::updateOrCreate(
                ['slug' => Str::slug($manufacturer['name'])],
                ['name' => $name],
            );
            if (! $dbManufacturer->logo) {
                $this->info("Copying logo for {$dbManufacturer->name}");

                $logoPath = "manufacturer-logos/{$dbManufacturer->slug}.png";
                Storage::disk('public')->put($logoPath, file_get_contents($manufacturer['logotype']['uri']));
                $dbManufacturer->logo = $logoPath;

                $dbManufacturer->save();
            }
        }

        $this->comment('All done');

        \App\Models\Sale::query()->where('vehicle_make', 'Mercedes-Benz')->update(['vehicle_make' => 'Mercedes']);

        \App\Models\Sale::query()->update([
            'manufacturer_id' => DB::raw('(SELECT id FROM manufacturers WHERE name = vehicle_make)'),
        ]);

        $this->comment('Associated with vehicles');

        return self::SUCCESS;
    }

    private function getManufacturers()
    {
        return json_decode(file_get_contents('https://raw.githubusercontent.com/cloudest-co/vehicle-logotypes/refs/heads/master/src/vehicle-logotypes.json'), true);
        //        return json_decode(file_get_contents(storage_path('data/vehicle-logotypes.json')), true);
    }
}
