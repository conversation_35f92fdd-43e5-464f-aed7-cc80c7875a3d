<?php

use App\Console\Commands\CreateMonthlyCustomerPaymentsCommand;
use App\Models\Account;
use App\Models\Dealership;
use App\Models\Payment;
use App\Models\Sale;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Artisan;
use Mo<PERSON>y\MockInterface;

test('monthly customer command creates payments for new sales in the current period', function ($startDate, $billingPeriodStart) {

    // 20th is the billing day so that we have enough time to
    // request payment for any date in the following month
    Carbon::setTestNow('2025-01-20');

    $this->mock(\App\Services\Payments\PaymentProcessor::class, function (MockInterface $mock) {
        $mock->shouldReceive('createPayment')
            ->withArgs(function (Payment $payment) {
                expect($payment)
                    ->amount->toEqual(35.99);

                return true;
            })
            ->andReturnUsing(fn () => new \App\Services\Payments\PaymentData(
                provider: \App\Enums\PaymentProvider::ACCESS_PAYSUITE,
                providerPaymentId: 'SOME-ACCESS-PAYSUITE-PAYMENT-ID',
                chargeDate: today()->addWeeks(2),
                status: Payment::STATUS_PENDING_SUBMISSION,
                amount: 0,
            ));
    });

    $account = Account::factory()->create();

    $sale = Sale::factory()
        ->confirmed()
        ->recycle($account)
        ->for(Dealership::withoutEvents(fn () => Dealership::factory()->recycle($account)))
        ->has(\App\Models\Warranty::factory()->subscription())
        ->create(fn () => [
            'start_date' => $startDate,
            'billing_request_id' => \App\Models\BillingRequest::factory(),
        ]);

    Artisan::call(CreateMonthlyCustomerPaymentsCommand::class);

    expect(Payment::count())->toBe($billingPeriodStart ? 1 : 0);

    if ($billingPeriodStart) {
        expect($sale->payments()->first())
            ->toBeInstanceOf(Payment::class)
            ->period_start->toDateString()->toBe($billingPeriodStart)
            ->processor_payment_id->toBe('SOME-ACCESS-PAYSUITE-PAYMENT-ID')
            ->amount->toEqual(35.99)
            ->status->toBe('pending_submission');
    }
})->with([
    'sale from earlier period' => ['2024-12-01', '2025-02-01'],
    'sale from current period' => ['2025-01-28', '2025-02-28'],
    'sale from later period' => ['2025-02-01', null],
    'sale from end of month to test shorter month billing period' => ['2025-01-31', '2025-03-01'],
]);
