<?php

namespace Tests\Feature\Console;

use App\Console\Commands\SendPaymentUpcomingPaymentNotifications;
use App\Models\Payment;
use App\Notifications\UpcomingPaymentNotification;
use Carbon\Carbon;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class SendPaymentUpcomingPaymentNotificationsTest extends TestCase
{
    public function test_it_sends_reminders_for_payments_due_in_three_days(): void
    {
        Notification::fake();

        Carbon::setTestNow(Carbon::now()->startOfSecond());

        // Create a payment due in 3 days (should get notification)
        $payment = Payment::factory()
            ->customer()
            ->create([
                'charge_date' => Carbon::now()->addDays(3),
            ]);

        // Create a none Access Paysuite payment due in 3 days (shouldn't get notification)
        $payment2 = Payment::factory()
            ->goCardless()
            ->customer()
            ->create([
                'charge_date' => Carbon::now()->addDays(3),
            ]);

        // Create a payment due in 2 days (shouldn't get notification)
        $payment3 = Payment::factory()
            ->customer()
            ->create([
                'charge_date' => Carbon::now()->addDays(2),
            ]);

        // Create a payment due in 3 days that has already been notified (shouldn't get notification)
        $payment4 = Payment::factory()
            ->customer()
            ->create([
                'charge_date' => Carbon::now()->addDays(3),
                'upcoming_payment_notification_sent_at' => Carbon::now(),
            ]);

        // Create a payment due in 4 days (shouldn't get notification)
        $payment5 = Payment::factory()
            ->customer()
            ->create([
                'charge_date' => Carbon::now()->addDays(4),
            ]);

        $this->artisan(SendPaymentUpcomingPaymentNotifications::class)->assertSuccessful();

        Notification::assertSentTo(
            $payment->payable->customer,
            UpcomingPaymentNotification::class,
            fn ($notification) => $notification->payment->id === $payment->id
        );

        // Assert only first notification was sent
        Notification::assertNothingSentTo($payment2->payable->customer);
        Notification::assertNothingSentTo($payment3->payable->customer);
        Notification::assertNothingSentTo($payment4->payable->customer);
        Notification::assertNothingSentTo($payment5->payable->customer);

        expect($payment->refresh())
            ->upcoming_payment_notification_sent_at->toEqual(now());
    }
}
