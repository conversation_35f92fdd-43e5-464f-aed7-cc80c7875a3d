<?php

use App\Filament\Resources\SaleResource\Pages\AddSaleProducts;
use App\Models\Account;
use App\Models\AccountBreakdownProduct;
use App\Models\AccountServicePlanProduct;
use App\Models\AccountWarrantyProduct;
use App\Models\Dealership;
use App\Models\Sale;
use App\Models\User;
use App\Notifications\CustomerDirectDebitMandateNotification;
use App\Notifications\CustomerWelcomeAndDocumentNotification;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Notification;

use function Pest\Laravel\actingAs;
use function Pest\Livewire\livewire;

describe('adding products to the sale', function () {

    beforeEach(function () {
        Dealership::unsetEventDispatcher();
        Notification::fake();
    });

    test('users without permission cannot render page', function () {
        $account = Account::factory()->create();

        $sale = Sale::factory()->recycle($account)->create();
        actingAs(User::factory()->recycle($account)->create());

        livewire(AddSaleProducts::class, ['record' => $sale->getRouteKey()])
            ->assertForbidden();
    });

    test('only dealer users from the same account can see the sale', function () {
        $accountA = Account::factory()->create();
        $accountB = Account::factory()->create();

        actingAs(User::factory()->recycle($accountA)->create()->givePermissionTo('sales.view', 'sales.create'));

        $sale = Sale::factory()->recycle($accountB)->create();

        livewire(AddSaleProducts::class, ['record' => $sale->id])->assertNotFound();

    })->throws(ModelNotFoundException::class);

    test('the form shows only warranties if the dealer account is only set up for warranties', function () {
        $account = Account::factory()->create();

        actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));

        $sale = Sale::factory()->recycle($account)->create();

        AccountWarrantyProduct::factory()->recycle($account)->create();

        livewire(AddSaleProducts::class, ['record' => $sale->id])
            ->assertSuccessful()
            ->assertFormFieldIsVisible('warranty.account_warranty_product_id')
            ->assertFormFieldIsHidden('breakdownPlan.account_breakdown_product_id')
            ->assertFormFieldIsHidden('servicePlan.account_service_plan_product_id');
    });

    test('the form shows only breakdown policies if the dealer account is only set up for warranties', function () {
        $account = Account::factory()->create();

        actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));

        $sale = Sale::factory()->recycle($account)->create();

        AccountBreakdownProduct::factory()->recycle($account)->create();

        livewire(AddSaleProducts::class, ['record' => $sale->id])
            ->assertSuccessful()
            ->assertFormFieldIsHidden('warranty.account_warranty_product_id')
            ->assertFormFieldIsVisible('breakdownPlan.account_breakdown_product_id')
            ->assertFormFieldIsHidden('servicePlan.account_service_plan_product_id');
    });

    test('the form shows only service plans if the dealer account is only set up for warranties', function () {
        $account = Account::factory()->create();

        actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));

        $sale = Sale::factory()->recycle($account)->create();

        AccountServicePlanProduct::factory()->recycle($account)->create();

        livewire(AddSaleProducts::class, ['record' => $sale->id])
            ->assertSuccessful()
            ->assertFormFieldIsHidden('warranty.account_warranty_product_id')
            ->assertFormFieldIsHidden('breakdownPlan.account_breakdown_product_id')
            ->assertFormFieldIsVisible('servicePlan.account_service_plan_product_id');
    });

    test('dealers can add just a warranty', function () {
        $account = Account::factory()->create();
        actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));
        $accountWarrantyProduct = AccountWarrantyProduct::factory()->recycle($account)->create();
        AccountBreakdownProduct::factory()->recycle($account)->create();
        AccountServicePlanProduct::factory()->recycle($account)->withAllServiceTypes()->create();

        $sale = Sale::factory()->recycle($account)->create();

        livewire(AddSaleProducts::class, ['record' => $sale->id])
            ->assertSuccessful()
            ->assertFormFieldIsHidden('warranty.selling_price')
            ->fillForm(['warranty.account_warranty_product_id' => $accountWarrantyProduct->id])
            ->assertFormFieldIsVisible('warranty.selling_price')
            ->assertFormSet(['warranty.selling_price' => $accountWarrantyProduct->selling_price])
            ->fillForm(['warranty.selling_price' => 111])
            ->call('save');

        Notification::assertNotSentTo($sale->customer, CustomerDirectDebitMandateNotification::class);
        Notification::assertSentTo($sale->customer, CustomerWelcomeAndDocumentNotification::class);

        expect($sale->warranty)
            ->product_id->toBe($accountWarrantyProduct->product_id)
            ->is_self_funded->toBe($sale->account->warranty_self_funded)
            ->provision->toBe($accountWarrantyProduct->provision)
            ->admin_fee->toBe($accountWarrantyProduct->admin_fee)
            ->selling_price->toBe(111)
            ->and($sale->breakdownPlan)->toBeNull()
            ->and($sale->servicePlan)->toBeNull();
    });

    test('dealers can add just a breakdown plan', function () {
        $account = Account::factory()->create();
        actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));
        $accountBreakdownProduct = AccountBreakdownProduct::factory()->recycle($account)->create();
        AccountWarrantyProduct::factory()->recycle($account)->create();
        AccountServicePlanProduct::factory()->recycle($account)->withAllServiceTypes()->create();

        $sale = Sale::factory()->recycle($account)->create();

        livewire(AddSaleProducts::class, ['record' => $sale->id])
            ->assertSuccessful()
            ->assertFormFieldIsHidden('breakdownPlan.selling_price')
            ->fillForm(['breakdownPlan.account_breakdown_product_id' => $accountBreakdownProduct->id])
            ->assertFormFieldIsVisible('breakdownPlan.selling_price')
            ->assertFormSet(['breakdownPlan.selling_price' => $accountBreakdownProduct->selling_price])
            ->fillForm(['breakdownPlan.selling_price' => 222])
            ->call('save');

        Notification::assertNotSentTo($sale->customer, CustomerDirectDebitMandateNotification::class);
        Notification::assertSentTo($sale->customer, CustomerWelcomeAndDocumentNotification::class);

        expect($sale->warranty)->toBeNull()
            ->and($sale->breakdownPlan)
            ->breakdown_product_id->toBe($accountBreakdownProduct->breakdown_product_id)
            ->is_self_funded->toBe($sale->account->breakdown_self_funded)
            ->provision->toBe($accountBreakdownProduct->provision)
            ->admin_fee->toBe($accountBreakdownProduct->admin_fee)
            ->selling_price->toBe(222)
            ->and($sale->servicePlan)->toBeNull();
    });

    test('dealers can add just a service plan', function () {
        $account = Account::factory()->create();
        actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));
        AccountWarrantyProduct::factory()->recycle($account)->create();
        AccountBreakdownProduct::factory()->recycle($account)->create();
        $accountServicePlanProduct = AccountServicePlanProduct::factory()->recycle($account)->withAllServiceTypes()->create();
        $sale = Sale::factory()->recycle($account)->create();

        livewire(AddSaleProducts::class, ['record' => $sale->id])
            ->assertSuccessful()
            ->assertFormFieldIsHidden('servicePlan.selling_price')
            ->fillForm(['servicePlan.account_service_plan_product_id' => $accountServicePlanProduct->id])
            ->assertFormFieldIsVisible('servicePlan.selling_price')
            ->assertFormSet(['servicePlan.selling_price' => $accountServicePlanProduct->selling_price])
            ->fillForm(['servicePlan.selling_price' => 333])
            ->call('save');

        Notification::assertNotSentTo($sale->customer, CustomerDirectDebitMandateNotification::class);
        Notification::assertSentTo($sale->customer, CustomerWelcomeAndDocumentNotification::class);

        expect($sale->warranty)->toBeNull()
            ->and($sale->breakdownPlan)->toBeNull()
            ->and($sale->servicePlan)
            ->service_plan_product_id->toBe($accountServicePlanProduct->service_plan_product_id)
            ->admin_fee->toBe($accountServicePlanProduct->admin_fee)
            ->selling_price->toBe(333)
            ->duration_years->toBe($accountServicePlanProduct->duration_years)
            ->serviceTypes->count()->toBe(3)
            ->serviceTypes->get(0)->pivot->limit->toBe(1)
            ->serviceTypes->get(1)->pivot->limit->toBe(2)
            ->serviceTypes->get(2)->pivot->limit->toBe(3);
    });

    test('dealers can add all products', function (bool $sendContractEmails) {
        $account = Account::factory()->create([
            'send_contract_emails' => $sendContractEmails,
        ]);
        actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));
        $accountWarrantyProduct = AccountWarrantyProduct::factory()->recycle($account)->create();
        $accountBreakdownProduct = AccountBreakdownProduct::factory()->recycle($account)->create();
        $accountServicePlanProduct = AccountServicePlanProduct::factory()->recycle($account)->withAllServiceTypes()->create();
        $sale = Sale::factory()->recycle($account)->create();

        livewire(AddSaleProducts::class, ['record' => $sale->id])
            ->assertSuccessful()
            ->assertFormFieldIsHidden('warranty.selling_price')
            ->assertFormFieldIsHidden('breakdownPlan.selling_price')
            ->assertFormFieldIsHidden('servicePlan.selling_price')
            ->fillForm([
                'warranty.account_warranty_product_id' => $accountWarrantyProduct->id,
                'breakdownPlan.account_breakdown_product_id' => $accountBreakdownProduct->id,
                'servicePlan.account_service_plan_product_id' => $accountServicePlanProduct->id,
            ])
            ->assertFormFieldIsVisible('warranty.selling_price')
            ->assertFormFieldIsVisible('breakdownPlan.selling_price')
            ->assertFormFieldIsVisible('servicePlan.selling_price')
            ->assertFormSet([
                'warranty.selling_price' => $accountWarrantyProduct->selling_price,
                'breakdownPlan.selling_price' => $accountBreakdownProduct->selling_price,
                'servicePlan.selling_price' => $accountServicePlanProduct->selling_price,
            ])
            ->fillForm([
                'warranty.selling_price' => 111,
                'breakdownPlan.selling_price' => 222,
                'servicePlan.selling_price' => 333,
            ])
            ->call('save');

        Notification::assertNotSentTo($sale->customer, CustomerDirectDebitMandateNotification::class);
        if ($sendContractEmails) {
            Notification::assertSentTo($sale->customer, CustomerWelcomeAndDocumentNotification::class);
        } else {
            Notification::assertNothingSent();
        }

        expect($sale->warranty)
            ->product_id->toBe($accountWarrantyProduct->product_id)
            ->is_self_funded->toBe($sale->account->warranty_self_funded)
            ->provision->toBe($accountWarrantyProduct->provision)
            ->admin_fee->toBe($accountWarrantyProduct->admin_fee)
            ->selling_price->toBe(111)
            ->isRecurring()->toBeFalse()
            ->and($sale->breakdownPlan)
            ->breakdown_product_id->toBe($accountBreakdownProduct->breakdown_product_id)
            ->is_self_funded->toBe($sale->account->breakdown_self_funded)
            ->provision->toBe($accountBreakdownProduct->provision)
            ->admin_fee->toBe($accountBreakdownProduct->admin_fee)
            ->selling_price->toBe(222)
            ->and($sale->servicePlan)
            ->service_plan_product_id->toBe($accountServicePlanProduct->service_plan_product_id)
            ->admin_fee->toBe($accountServicePlanProduct->admin_fee)
            ->selling_price->toBe(333)
            ->duration_years->toBe($accountServicePlanProduct->duration_years)
            ->serviceTypes->count()->toBe(3)
            ->serviceTypes->get(0)->pivot->limit->toBe(1)
            ->serviceTypes->get(1)->pivot->limit->toBe(2)
            ->serviceTypes->get(2)->pivot->limit->toBe(3);
    })->with([
        'send contract emails' => [true],
        'do not send contract emails' => [false],
    ]);

    test('dealers can add a subscription warranty', function ($upfrontDealerPayment) {
        \Illuminate\Support\Carbon::setTestNow(now()->startOfHour());

        $account = Account::factory()->create();
        actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));
        $accountWarrantyProduct = AccountWarrantyProduct::factory()->subscription()->recycle($account)->create([
            'selling_price' => $upfrontDealerPayment,
        ]);
        AccountBreakdownProduct::factory()->recycle($account)->create();
        AccountServicePlanProduct::factory()->recycle($account)->withAllServiceTypes()->create();

        $sale = Sale::factory()->recycle($account)->create();

        livewire(AddSaleProducts::class, ['record' => $sale->id])
            ->assertSuccessful()
            ->assertFormFieldIsHidden('warranty.selling_price')
            ->fillForm(['warranty.account_warranty_product_id' => $accountWarrantyProduct->id])
            ->assertFormFieldIsVisible('warranty.selling_price')
            ->call('save');

        $sale->refresh();

        Notification::assertSentTo($sale->customer, CustomerDirectDebitMandateNotification::class);
        Notification::assertNotSentTo($sale->customer, CustomerWelcomeAndDocumentNotification::class);

        expect($sale->warranty)
            ->product_id->toBe($accountWarrantyProduct->product_id)
            ->is_self_funded->toBe($sale->account->warranty_self_funded)
            ->provision->toBe($accountWarrantyProduct->provision)
            ->admin_fee->toBe($accountWarrantyProduct->admin_fee)
            ->selling_price->toBe($upfrontDealerPayment)
            ->monthly_selling_price->toBe($accountWarrantyProduct->monthly_selling_price)
            ->monthly_admin_fee->toBe($accountWarrantyProduct->monthly_admin_fee)
            ->isRecurring()->toBeTrue();

        expect($sale->breakdownPlan)->toBeNull();

        expect($sale->servicePlan)->toBeNull();

        expect($sale->billingRequest()->first())
            ->mandate_url->not->toBeNull()
            ->expires_at->toEqual(now()->addDays(7))
            ->visited_at->toBeNull()
            ->status->toBe('pending');

        expect($sale->payments()->count())->toBe(0);

    })->with([
        'without upfront dealer payment' => [0],
        'with upfront dealer payment' => [99],
    ]);
});
