<?php

use App\Filament\Resources\SaleResource\Pages\AddSaleProducts;
use App\Filament\Resources\SaleResource\Pages\ViewSale;
use App\Models\Account;
use App\Models\BillingRequest;
use App\Models\Sale;
use App\Models\User;
use App\Models\Warranty;

use function Pest\Laravel\actingAs;
use function Pest\Livewire\livewire;

beforeEach(function () {
    $this->account = Account::factory()->create();
});

test('users without permission cannot view a sale', function () {
    actingAs(User::factory()->recycle($this->account)->create());
    $sale = Sale::factory()->recycle($this->account)->create();

    livewire(ViewSale::class, ['record' => $sale->getKey()])
        ->assertForbidden();
});

test('users from a different account cannot view a sale', function () {
    actingAs(User::factory()->recycle($this->account)->create());
    $sale = Sale::factory()->create();

    livewire(ViewSale::class, ['record' => $sale->getKey()])
        ->assertNotFound();
})->todo('cannot get this working');

test('users with permission will get a redirect if the sale is not confirmed', function () {
    actingAs(User::factory()->recycle($this->account)->create()->givePermissionTo('sales.view'));
    $sale = Sale::factory()->recycle($this->account)->create();

    livewire(ViewSale::class, ['record' => $sale->getKey()])
        ->assertRedirect(AddSaleProducts::getUrl([$sale->getKey()]));

});

test('users with permission will see a sale if it is confirmed', function () {
    actingAs(User::factory()->recycle($this->account)->create()->givePermissionTo('sales.view'));
    $sale = Sale::factory()->confirmed($this->account)->create();

    livewire(ViewSale::class, ['record' => $sale->getKey()])
        ->assertOk()
        ->assertSee($sale->formattedVrm())
        ->assertActionVisible('resend-documents')
        ->assertActionVisible('print-sale-documents');
});

test('users with permission will not see document generation actions if the subscription sale is awaiting direct debit setup', function () {
    actingAs(User::factory()->recycle($this->account)->create()->givePermissionTo('sales.view'));
    $sale = Sale::factory()->confirmed($this->account)
        ->has(Warranty::factory()->subscription())
        ->create();

    expect($sale->requiresPaymentSetup())->toBeTrue();

    livewire(ViewSale::class, ['record' => $sale->getKey()])
        ->assertOk()
        ->assertSee($sale->formattedVrm())
        ->assertActionHidden('resend-documents')
        ->assertActionHidden('print-sale-documents');
});

test('users with permission will can see document generation actions if the subscription sale has direct debit setup', function () {
    actingAs(User::factory()->recycle($this->account)->create()->givePermissionTo('sales.view'));
    $sale = Sale::factory()
        ->confirmed($this->account)
        ->has(Warranty::factory()->subscription())
        ->for(BillingRequest::factory()->completed())
        ->create();

    expect($sale->requiresPaymentSetup())->toBeFalse();

    livewire(ViewSale::class, ['record' => $sale->getKey()])
        ->assertOk()
        ->assertSee($sale->formattedVrm())
        ->assertActionVisible('resend-documents')
        ->assertActionVisible('print-sale-documents');
});
