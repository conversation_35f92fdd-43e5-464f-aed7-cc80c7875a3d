<?php

use App\Filament\Resources\SaleResource\Pages\ListSales;
use App\Models\Account;
use App\Models\Sale;
use App\Models\User;
use App\Services\Accounting\XeroAccountingService;
use Mo<PERSON>y\MockInterface;

use function Pest\Laravel\actingAs;
use function Pest\Livewire\livewire;

beforeEach(function () {
    $this->mock(XeroAccountingService::class, function (MockInterface $mock) {
        $mock->shouldReceive('createContact')
            ->andReturnUsing(function (\App\Services\Accounting\ContactData $contactData) {
                $contactData->id = 'SOME-XERO-CONTACT-ID';

                return $contactData;
            });
    });
});

test('rendering sales list shows sales', function () {
    $account = Account::factory()->create();

    actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view'));

    $confirmedSale = Sale::factory()->confirmed($account)->create();

    livewire(ListSales::class)
        ->assertSuccessful()
        ->assertTableColumnStateSet('id', $confirmedSale->id, $confirmedSale);
});
