<?php

use App\Filament\Resources\CustomerResource\Pages\CreateCustomer;
use App\Filament\Resources\SaleResource;
use App\Models\Account;
use App\Models\Customer;
use App\Models\User;

use function Pest\Laravel\actingAs;
use function Pest\Livewire\livewire;

describe('creating a customer', function () {

    test('users without permission cannot render page', function () {
        actingAs(User::factory()->create());

        livewire(CreateCustomer::class)->assertForbidden();
    });

    test('dealers can create a customer', function () {
        $account = Account::factory()->create();

        actingAs(User::factory()->recycle($account)->create()->givePermissionTo('customers.create'));

        $customerData = Arr::except(Customer::factory()->make()->toArray(), ['account_id']);
        livewire(CreateCustomer::class)
            ->assertSuccessful()
            ->assertFormFieldExists('first_name')
            ->assertFormFieldExists('last_name')
            ->assertFormFieldExists('email')
            ->assertFormFieldExists('phone')
            ->assertFormFieldExists('address_1')
            ->assertFormFieldExists('address_2')
            ->assertFormFieldExists('city')
            ->assertFormFieldExists('county')
            ->assertFormFieldExists('country')
            ->assertFormFieldExists('postcode')
            ->call('create')
            ->assertHasFormErrors(['first_name', 'last_name', 'email', 'phone', 'address_1', 'city', 'county', 'country', 'postcode'])
            ->fillForm($customerData)
            ->call('create')
            ->assertHasNoFormErrors()
            ->assertRedirect(SaleResource::getUrl('create', ['customer' => Customer::first()]));

        expect(Customer::count())->toBe(1)
            ->and(Customer::first())
            ->account_id->toBe(auth()->user()->account_id)
            ->first_name->toBe($customerData['first_name'])
            ->last_name->toBe($customerData['last_name'])
            ->email->toBe($customerData['email'])
            ->phone->toBe($customerData['phone'])
            ->address_1->toBe($customerData['address_1'])
            ->address_2->toBe($customerData['address_2'])
            ->city->toBe($customerData['city'])
            ->county->toBe($customerData['county'])
            ->country->toBe($customerData['country'])
            ->postcode->toBe($customerData['postcode']);
    });

});
