<?php

use <PERSON><PERSON><PERSON>\MockInterface;

use function Pest\Laravel\mock;

function getPaymentAssistPayload($filename)
{
    return json_decode(
        file_get_contents(base_path("tests/data/webhooks/payment_assist/{$filename}")),
        true
    );
}

test('application status update webhook', function () {
    $payLaterAgreement = \App\Models\PayLaterAgreement::factory()->create([
        'token' => 'b1e45648-f369-11ea-8ed1-2cfda158243b',
    ]);

    mock(\App\Services\Payments\PayLater\PaymentAssistProcessor::class, function (MockInterface $mock) use ($payLaterAgreement) {
        $mock->shouldReceive('getApplicationStatus')
            ->once()
            ->withArgs([$payLaterAgreement->token])
            ->andReturnUsing(fn (string $token) => new \App\Services\Payments\PayLater\ApplicationStatusData(
                token: $token,
                providerReference: '<SOME-REFERENCE>',
                status: 'failed',
                planId: '<SOME-PLAN-ID>',
                requiresInvoice: false,
                hasInvoice: false,
                lastAccessedAt: now(),
            ));
    });

    $this
        ->postJson(route('webhooks.payment-assist'), getPaymentAssistPayload('application_failed.json'))
        ->assertOk();

    expect($payLaterAgreement->refresh())
        ->status->toBe('failed')
        ->provider_reference->toBe('<SOME-REFERENCE>');
});
