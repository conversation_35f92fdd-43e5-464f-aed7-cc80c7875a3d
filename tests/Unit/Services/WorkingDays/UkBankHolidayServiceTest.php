<?php

use App\Services\WorkingDays\UkBankHolidayService;
use Illuminate\Cache\CacheManager;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;

uses(\Tests\TestCase::class);

beforeEach(function () {
    $this->cache = Mockery::mock(CacheManager::class);
    $this->config = [
        'url' => 'https://www.gov.uk/bank-holidays.json',
        'cache_ttl' => 86400,
    ];
    $this->service = new UkBankHolidayService($this->cache, $this->config);
});

describe('getBankHolidays', function () {
    it('returns cached bank holidays when available', function () {
        $expectedHolidays = collect([
            Carbon::parse('2024-01-01'),
            Carbon::parse('2024-12-25'),
        ]);

        $this->cache
            ->shouldReceive('remember')
            ->once()
            ->with('uk_bank_holidays_england_wales', 86400, \Mockery::type('callable'))
            ->andReturn($expectedHolidays);

        $result = $this->service->getBankHolidays();

        expect($result)->toBe($expectedHolidays);
    });

    it('fetches and caches bank holidays from API when not cached', function () {
        $apiResponse = [
            'england-and-wales' => [
                'events' => [
                    ['date' => '2024-01-01', 'title' => 'New Year\'s Day'],
                    ['date' => '2024-12-25', 'title' => 'Christmas Day'],
                ],
            ],
        ];

        Http::fake([
            'https://www.gov.uk/bank-holidays.json' => Http::response($apiResponse),
        ]);

        $this->cache
            ->shouldReceive('remember')
            ->once()
            ->with('uk_bank_holidays_england_wales', 86400, \Mockery::type('callable'))
            ->andReturnUsing(function ($key, $ttl, $callback) {
                return $callback();
            });

        $result = $this->service->getBankHolidays();

        expect($result)->toHaveCount(2);
        expect($result->first()->toDateString())->toBe('2024-01-01');
        expect($result->last()->toDateString())->toBe('2024-12-25');
    });

    it('handles API failures gracefully', function () {
        Http::fake([
            'https://www.gov.uk/bank-holidays.json' => Http::response([], 500),
        ]);

        $this->cache
            ->shouldReceive('remember')
            ->once()
            ->andReturnUsing(function ($key, $ttl, $callback) {
                return $callback();
            });

        $result = $this->service->getBankHolidays();

        expect($result)->toBeEmpty();
    });

    it('handles invalid API response format', function () {
        Http::fake([
            'https://www.gov.uk/bank-holidays.json' => Http::response(['invalid' => 'format']),
        ]);

        $this->cache
            ->shouldReceive('remember')
            ->once()
            ->andReturnUsing(function ($key, $ttl, $callback) {
                return $callback();
            });

        expect(fn () => $this->service->getBankHolidays())
            ->toThrow(RuntimeException::class, 'Invalid response format from UK bank holidays API');
    });
});

describe('isBankHoliday', function () {
    it('returns true when date is a bank holiday', function () {
        $testDate = Carbon::parse('2024-01-01');
        $bankHolidays = collect([
            Carbon::parse('2024-01-01'),
            Carbon::parse('2024-12-25'),
        ]);

        $this->cache
            ->shouldReceive('remember')
            ->once()
            ->andReturn($bankHolidays);

        $result = $this->service->isBankHoliday($testDate);

        expect($result)->toBeTrue();
    });

    it('returns false when date is not a bank holiday', function () {
        $testDate = Carbon::parse('2024-01-02');
        $bankHolidays = collect([
            Carbon::parse('2024-01-01'),
            Carbon::parse('2024-12-25'),
        ]);

        $this->cache
            ->shouldReceive('remember')
            ->once()
            ->andReturn($bankHolidays);

        $result = $this->service->isBankHoliday($testDate);

        expect($result)->toBeFalse();
    });

    it('compares dates correctly regardless of time', function () {
        $testDate = Carbon::parse('2024-01-01 15:30:00');
        $bankHolidays = collect([
            Carbon::parse('2024-01-01 00:00:00'),
        ]);

        $this->cache
            ->shouldReceive('remember')
            ->once()
            ->andReturn($bankHolidays);

        $result = $this->service->isBankHoliday($testDate);

        expect($result)->toBeTrue();
    });
});

describe('clearCache', function () {
    it('clears the bank holidays cache', function () {
        $this->cache
            ->shouldReceive('forget')
            ->once()
            ->with('uk_bank_holidays_england_wales');

        $this->service->clearCache();
    });
});
